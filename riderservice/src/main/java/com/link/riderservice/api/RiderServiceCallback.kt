package com.link.riderservice.api

import android.bluetooth.BluetoothDevice
import android.view.Display
import com.link.riderservice.api.dto.Connection
import com.link.riderservice.api.dto.NaviMode
import com.link.riderservice.api.dto.RiderServiceConfig
import com.link.riderservice.connection.ble.BleDevice

/**
 * RiderService 回调
 */
abstract class RiderServiceCallback {
    /**
     * 返回搜索到的ble设备
     * @param devices ble设备列表
     */
    open fun onScanResult(devices: List<BleDevice>) {}

    /**
     * 通知Ble正在搜索
     */
    open fun onScanning() {}

    /**
     * 通知Ble搜索完成
     */
    open fun onScanFinish() {}

    /**
     * 通知连接状态的变化
     * @param status [Connection]
     */
    open fun onConnectStatusChange(status: Connection) {}

    /**
     * 通知需要蓝牙扫描权限
     */
    open fun onNeedBluetoothScanPermission() {}

    /**
     * 通知需要打开蓝牙
     */
    open fun onRequestOpenBluetooth() {}

    /**
     * 通知需要位置权限
     */
    open fun onNeedLocationPermission() {}

    /**
     * 通知仪表盘的配置改变,只有在连接成功后才会回调
     * @param config [RiderServiceConfig]
     */
    open fun onConfigChange(config: RiderServiceConfig) {}

    /**
     * 通知方控控制的导航模式改变
     * @param naviMode
     * @see NaviMode
     */
    open fun onNaviModeChange(naviMode: NaviMode) {}

    /**
     * 通知投屏导航的屏幕已经准备好
     * @param display
     * @see display
     */
    open fun onDisplayInitialized(display: Display) {}

    /**
     * 通知投屏导航的屏幕已经释放
     * @param display
     * @see display
     */
    open fun onDisplayReleased(display: Display) {}

    /**
     * 视频频道准备好，可以投屏
     */
    open fun onVideoChannelReady() {}

    /**
     * 通知 RiderService 需要天气信息
     */
    open fun onRequestWeatherInfo() {}

    /**
     * 通知 Cluster 已经准备好
     */
    open fun onClusterReady() {}

    /**
     * 通知导航模式切换
     */
    open fun onNaviModeChangeResponse(mode: NaviMode, ready: Boolean) {}
    open fun onRequestMediaProjection() {}
    open fun onMirrorStart() {}
    open fun onMirrorStop() {}
    open fun onNaviModeStopResponse(naviMode: NaviMode) {}
    open fun onNaviModeStartResponse(naviMode: NaviMode) {}
    open fun onEnableNotificationFailed(device: BluetoothDevice, status: Int) {}
    open fun onWifiState(opened: Boolean) {}

    /**
     * 在app层打印dialog
     */
    open fun onDialogShow(title:String, message: String) {}
    open fun changeMap(type: Int) {}
    open fun naviversionResponse(version: String) {}
}