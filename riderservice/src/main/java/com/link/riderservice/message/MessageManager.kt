package com.link.riderservice.message

import android.util.Log
import com.google.protobuf.MessageLite
import com.link.riderservice.ext.readInt16BE
import com.link.riderservice.ext.readInt32BE
import com.link.riderservice.ext.readUInt8
import com.link.riderservice.ext.writeInt16BE
import com.link.riderservice.protobuf.RiderProtocol
import java.lang.ref.WeakReference
import java.nio.ByteBuffer
import java.util.concurrent.ConcurrentLinkedDeque

/**
 * <AUTHOR>
 * @date 2022/6/29
 */
internal class MessageManager {
    private val mIncoming = ConcurrentLinkedDeque<NaviMessage>()
    private val mOutgoing = ConcurrentLinkedDeque<NaviMessage>()
    private var mCallback: WeakReference<MessageCallback>? = null

    /**
     * 注册回调
     */
    fun registerCallback(callback: MessageCallback) {
        mCallback = WeakReference(callback)
    }

    /**
     * 注销回调
     */
    fun unregisterCallback() {
        mCallback = null
    }

    /**
     * 出队发送数据
     */
    fun dequeueOutgoing(): NaviMessage? {
        return mOutgoing.poll()
    }

    /**
     * 入队发送数据
     */
    fun queueOutgoing(messageId: Int, proto: MessageLite) {
        val len = proto.serializedSize
        val byteBuffer = ByteBuffer.allocate(len + MESSAGE_ID_LENGTH)
        val data = byteBuffer.array()
        data.writeInt16BE(messageId)
        System.arraycopy(proto.toByteArray(), 0, data, MESSAGE_ID_LENGTH, len)
        messageToFrames(data, data.size)
    }

    /**
     * 入队接收到的数据
     */
    fun enqueueIncoming(buf: ByteArray, size: Int) {
        decodeFrame(ByteBuffer.wrap(buf, 0, size))
    }

    private fun enqueueIncoming(message: NaviMessage) {
        mIncoming.addLast(message)
    }

    /**
     * 清除所有缓存信息
     */
    fun clearMessage() {
        mOutgoing.clear()
        mIncoming.clear()
    }

    private fun peekLastIncoming(): NaviMessage? {
        return mIncoming.peekLast()
    }

    private fun hasIncoming(): Boolean {
        return !mIncoming.isEmpty()
    }

    private fun dequeueIncoming(): NaviMessage? {
        return mIncoming.poll()
    }

    private fun hasOutgoing(): Boolean {
        return !mOutgoing.isEmpty()
    }

    private fun enqueueOutgoing(message: NaviMessage) {
        mOutgoing.addLast(message)
    }

    private fun messageToFrames(data: ByteArray, len: Int) {
        val message = ByteBuffer.wrap(data)
        var (fragInfo, fragLen) = calculateFragInfoAndLen(len)
        var remaining = len
        var offset = 0
        while (remaining > 0) {
            val payload = ByteArray(fragLen)
            message.position(offset)
            message.get(payload)
            val naviMessage = NaviMessage(
                bitField = fragInfo,
                payload = payload,
                frameLength = fragLen,
                messageLength = if (fragInfo == RiderProtocol.FragInfo.FRAG_FIRST_VALUE) len else 0
            )
            offset += fragLen
            remaining -= fragLen
            val (newFragInfo, newFragLen) = calculateFragInfoAndLen(remaining)
            fragInfo = newFragInfo
            fragLen = newFragLen
            enqueueOutgoing(naviMessage)
        }
    }

    private fun calculateFragInfoAndLen(len: Int): Pair<Int, Int> {
        return if (len > MAX_PAYLOAD_SIZE) {
            RiderProtocol.FragInfo.FRAG_FIRST_VALUE to MAX_PAYLOAD_SIZE
        } else {
            RiderProtocol.FragInfo.FRAG_UNFRAGMENTED_VALUE to len
        }
    }

    private fun framesToMessage(): ByteArray? {
        var frame = dequeueIncoming()
        if (frame?.bitField == RiderProtocol.FragInfo.FRAG_UNFRAGMENTED_VALUE) {
            return frame.payload
        }
        var offset = 0
        if (frame != null) {
            val msg = ByteArray(frame.messageLength)
            while (true) {
                frame?.let { data ->
                    data.payload?.let { payload ->
                        System.arraycopy(payload, 0, msg, offset, data.frameLength)
                    }
                    offset += data.frameLength
                }
                if (!hasIncoming()) {
                    break
                }
                frame = dequeueIncoming()
            }
            return msg
        }
        return null
    }

    private fun decodeFrame(buf: ByteBuffer) {
        val bitField = buf.array().readUInt8()
        val frameLength = buf.array().readInt16BE(BITFIELD_LENGTH)
        val messageLength =
            if (bitField == RiderProtocol.FragInfo.FRAG_FIRST_VALUE) buf.array()
                .readInt32BE(FRAME_LENGTH + BITFIELD_LENGTH) else 0
        val offset = if (messageLength == 0)
            FRAME_LENGTH + BITFIELD_LENGTH
        else
            FRAME_LENGTH + BITFIELD_LENGTH + MESSAGE_LENGTH
        val size = buf.limit() - offset
        val payload = ByteArray(size)
        buf.position(offset)
        buf.get(payload)
        val frame = NaviMessage(
            bitField = bitField,
            frameLength = frameLength,
            messageLength = messageLength,
            payload = payload,
            original = buf.array()
        )
        val lastFrame = peekLastIncoming()
        val hasIncoming = hasIncoming()
        val valid = isValid(frame, lastFrame, hasIncoming)
        val complete = isComplete(frame, lastFrame, hasIncoming)
        if (!valid) {
            Log.w(TAG, "invalid frame")
        }
        enqueueIncoming(frame)
        if (complete) {
            val msg = framesToMessage()
            val type = msg?.readInt16BE() ?: MESSAGE_TYPE_INVALID
            routeMessage(type, msg)
        }
    }

    private fun isValid(
        frame: NaviMessage,
        lastFrame: NaviMessage?,
        hasIncoming: Boolean
    ): Boolean {
        return when {
            hasIncoming -> {
                val validFirstOrContinuation =
                    lastFrame?.bitField == RiderProtocol.FragInfo.FRAG_FIRST_VALUE
                            || lastFrame?.bitField == RiderProtocol.FragInfo.FRAG_CONTINUATION_VALUE
                val validContinuationOrLast =
                    frame.bitField == RiderProtocol.FragInfo.FRAG_CONTINUATION_VALUE
                            || frame.bitField == RiderProtocol.FragInfo.FRAG_LAST_VALUE
                validFirstOrContinuation && validContinuationOrLast
            }

            else -> {
                frame.bitField == RiderProtocol.FragInfo.FRAG_UNFRAGMENTED_VALUE
                        || frame.bitField == RiderProtocol.FragInfo.FRAG_FIRST_VALUE
            }
        }
    }

    private fun isComplete(
        frame: NaviMessage,
        lastFrame: NaviMessage?,
        hasIncoming: Boolean
    ): Boolean {
        return when {
            hasIncoming -> {
                val validFirstOrContinuation =
                    lastFrame?.bitField == RiderProtocol.FragInfo.FRAG_FIRST_VALUE
                            || lastFrame?.bitField == RiderProtocol.FragInfo.FRAG_CONTINUATION_VALUE
                val validLast = frame.bitField == RiderProtocol.FragInfo.FRAG_LAST_VALUE
                validFirstOrContinuation && validLast
            }

            else -> {
                frame.bitField == RiderProtocol.FragInfo.FRAG_UNFRAGMENTED_VALUE
            }
        }
    }

    private fun routeMessage(type: Int, msg: ByteArray?) {
        if (type == -1) {
            return
        }
        val payLoad = msg?.copyOfRange(MESSAGE_ID_LENGTH, msg.size)
        mCallback?.get()?.onMessage(type, payLoad)
    }

    companion object {
        private const val TAG = "MessageManager"
        private const val MAX_PAYLOAD_SIZE = 90
        private const val BITFIELD_LENGTH = 1
        private const val FRAME_LENGTH = 2
        private const val MESSAGE_LENGTH = 4
        private const val MESSAGE_ID_LENGTH = 2
        private const val MESSAGE_TYPE_INVALID = -1

    }
}