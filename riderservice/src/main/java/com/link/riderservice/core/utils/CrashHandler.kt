package com.link.riderservice.core.utils

import android.os.Process
import com.link.riderservice.core.utils.logging.logE

/**
 * <AUTHOR>
 * @date 2018/11/21
 */
internal object CrashHandler : Thread.UncaughtExceptionHandler {
    private const val TAG = "CrashHandler"
    private var defaultCrashHandler: Thread.UncaughtExceptionHandler? = null
    
    fun init() {
        defaultCrashHandler = Thread.getDefaultUncaughtExceptionHandler()
        Thread.setDefaultUncaughtExceptionHandler(this)
    }

    override fun uncaughtException(thread: Thread, throwable: Throwable) {
        logE(TAG, "uncaughtException", throwable)
        if (defaultCrashHandler != null) {
            defaultCrashHandler?.uncaughtException(thread, throwable)
        } else {
            Process.killProcess(Process.myPid())
        }
    }
}