package com.link.riderservice.utils

import android.os.Process
import android.util.Log

/**
 * <AUTHOR>
 * @date 2018/11/21
 */
internal object CrashHandler : Thread.UncaughtExceptionHandler {
    private const val TAG = "CrashHandler"
    private var mDefaultCrashHandler: Thread.UncaughtExceptionHandler? = null
    fun init() {
        mDefaultCrashHandler = Thread.getDefaultUncaughtExceptionHandler()
        Thread.setDefaultUncaughtExceptionHandler(this)
    }

    override fun uncaughtException(t: Thread, e: Throwable) {
        Log.e(TAG, "uncaughtException",e)
        if (mDefaultCrashHandler != null) {
            mDefaultCrashHandler?.uncaughtException(t, e)
        } else {
            Process.killProcess(Process.myPid())
        }
    }
}