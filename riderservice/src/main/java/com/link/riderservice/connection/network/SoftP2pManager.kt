package com.link.riderservice.connection.network

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.net.NetworkInfo
import android.net.wifi.WifiManager
import android.net.wifi.p2p.WifiP2pConfig
import android.net.wifi.p2p.WifiP2pDevice
import android.net.wifi.p2p.WifiP2pDeviceList
import android.net.wifi.p2p.WifiP2pManager
import android.net.wifi.p2p.WifiP2pManager.ActionListener
import android.net.wifi.p2p.WifiP2pManager.ChannelListener
import android.util.Log
import com.link.riderservice.api.RiderService
import com.link.riderservice.utils.TimeUtils
import com.link.riderservice.utils.countDownByFlow
import com.link.riderservice.utils.mainScope
import kotlinx.coroutines.Job
import java.util.concurrent.atomic.AtomicBoolean

/**
 * <AUTHOR>
 * @date 2022/8/25
 * @desc WLAN 直连工具类
 */
internal class SoftP2pManager(
    val listener: SoftIP2pListener
) : ChannelListener,
    WifiP2pManager.PeerListListener {
    private val context = RiderService.instance.getApplication().applicationContext
    private val manager: WifiP2pManager? by lazy(LazyThreadSafetyMode.NONE) {
        context.getSystemService(Context.WIFI_P2P_SERVICE) as WifiP2pManager?
    }

    private val wifiManager: WifiManager? by lazy(LazyThreadSafetyMode.NONE) {
        context.getSystemService(Context.WIFI_SERVICE) as WifiManager?
    }
    private var mChannel: WifiP2pManager.Channel? =
        manager?.initialize(context, context.mainLooper, null)
    private var mPeerAddress: String = ""
    private var mPeerPort: Int = 0
    private var mIsConnecting = AtomicBoolean(false)
    private var retryChannel = false
    private var isWifiExist = false
    private var mSearchCountDown: Job? = null
    private var mConnectCountDown: Job? = null
    private var autolinkStation = AUTOLINK_STATION_WIFI_START
    private var mIsScanning = false
    private var mIsWifiConnected = false

    /** wifi连接重试次数*/
    private var total = 0

    // 用于监听 WLAN 直连状态的广播
    private val mReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        @SuppressLint("MissingPermission")
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                WifiManager.WIFI_STATE_CHANGED_ACTION -> {
                    when (intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, 0)) {
                        WifiManager.WIFI_STATE_DISABLED -> {
                            mIsConnecting.set(false)
                            listener.onWifiState(false)
                            autolinkStation = AUTOLINK_STATION_WIFI_DISABLE
                        }

                        WifiManager.WIFI_STATE_ENABLING -> {
                            if (autolinkStation == AUTOLINK_STATION_WIFI_DISABLE) {
                                listener.requestWifiInfo()
                            }
                        }

                        WifiManager.WIFI_STATE_ENABLED -> {
                            listener.onWifiState(true)
                        }
                    }
                }

                WifiP2pManager.WIFI_P2P_DISCOVERY_CHANGED_ACTION -> {
                    val discoveryState = intent.getIntExtra(
                        WifiP2pManager.EXTRA_DISCOVERY_STATE,
                        WifiP2pManager.WIFI_P2P_DISCOVERY_STOPPED
                    )
                    mIsScanning = discoveryState == WifiP2pManager.WIFI_P2P_DISCOVERY_STARTED
                    if(mIsScanning){
                        Log.d("connect analysis:", "start wifi scan::${TimeUtils.getCurrentTimeStr()}")

                    }else{

                        Log.d("connect analysis:", "end wifi scan::${TimeUtils.getCurrentTimeStr()}")
                    }
//                    Log.e(TAG, "connect analysis:WIFI_P2P_DISCOVERY_CHANGED_ACTION ${discoveryState == WifiP2pManager.WIFI_P2P_DISCOVERY_STARTED}")
                    discoverPeers()
                }

                WifiP2pManager.WIFI_P2P_STATE_CHANGED_ACTION -> {
                    Log.e("connect analysis:", "WIFI_P2P_STATE_CHANGED_ACTION::${TimeUtils.getCurrentTimeStr()}")
                    if (intent.getIntExtra(
                            WifiP2pManager.EXTRA_WIFI_STATE,
                            WifiP2pManager.WIFI_P2P_STATE_DISABLED
                        ) == WifiP2pManager.WIFI_P2P_STATE_ENABLED
                    ) {
                        discoverPeers()
                    }
                }

                WifiP2pManager.WIFI_P2P_PEERS_CHANGED_ACTION -> {
//                    Log.e("connect analysis:", "WIFI_P2P_PEERS_CHANGED_ACTION::${getTimeFormatter()}")
                    manager?.requestPeers(mChannel, this@SoftP2pManager)
                }

                WifiP2pManager.WIFI_P2P_CONNECTION_CHANGED_ACTION -> {
                    val networkInfo =
                        intent.getParcelableExtra<NetworkInfo>(WifiP2pManager.EXTRA_NETWORK_INFO)
                    Log.d(TAG, "P2P connection changed: ${networkInfo?.isConnected}")

                    if (networkInfo?.isConnected == true) {
                        Log.d("connect analysis:", "end wifi connect::${TimeUtils.getCurrentTimeStr()}}")
                        if (autolinkStationChange(AUTOLINK_STATION_CONNECT_SUCCESS)) {
                            total = 0
                            mIsWifiConnected = true
                            mConnectCountDown?.cancel()
                            listener.onWifiConnectSuccess()
                            stopSearch()
                        }
                    } else {
                        if (mIsWifiConnected) {
                            mIsWifiConnected = false
                            mIsConnecting.set(false)
                        }
                        listener.onWifiDisconnect()
                    }
                }

                WifiP2pManager.WIFI_P2P_THIS_DEVICE_CHANGED_ACTION -> {
                    val device =
                        intent.getParcelableExtra<WifiP2pDevice>(WifiP2pManager.EXTRA_WIFI_P2P_DEVICE)
                    Log.d(TAG, "P2P device status: ${device?.status}")
                    if (device?.status == WifiP2pDevice.CONNECTED && !mIsConnecting.get()) {
                        Log.d(TAG, "P2P device connected: ${device.deviceName}")
                        disconnect()
                        listener.requestWifiInfo()
                    }
                }
            }
        }
    }

    /**
     * 初始化 WLAN 直连
     */
    @SuppressLint("MissingPermission")
    internal fun start(address: String, port: Int) {
        Log.d(TAG, "connect analysis:start:mIsScanning = $mIsScanning")
        if (mIsScanning) {
            stop()
        }
        Log.d(TAG, "p2p module start")
        mIsConnecting.set(false)
        mPeerAddress = address
        mPeerPort = port
        autolinkStationChange(AUTOLINK_STATION_WIFI_START)
        val wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
        if (!wifiManager.isWifiEnabled) {
            listener.onWifiState(false)
        }
        //判断是否已经连接
        manager?.requestConnectionInfo(mChannel) {
            if (it.groupFormed) {
                if (autolinkStationChange(AUTOLINK_STATION_ALREADY_CONNECTED)) {
                    mIsWifiConnected = true
                    mIsConnecting.set(true)
                }
            } else {
                if (autolinkStationChange(AUTOLINK_STATION_NO_CONNECT)) {
                    total = 0
                    searchExist()
                }
            }
        }
        //开启搜索
        discoverPeers()
    }

    internal fun stop() {
        Log.d(TAG, "p2p module stop")
        //停止搜索
        stopSearch()
        //断开连接
        disconnect()
        //注销广播
//        unregisterReceiver()
        mConnectCountDown?.cancel()
        mConnectCountDown = null
        mIsWifiConnected = false
    }

    private fun initP2p(): Boolean {
        if (!context.packageManager.hasSystemFeature(PackageManager.FEATURE_WIFI_DIRECT)) {
            Log.e(TAG, "Wi-Fi Direct is not supported by this device.")
            return false
        }
        if (wifiManager == null) {
            Log.e(TAG, "Cannot get Wi-Fi system service.")
            return false
        }
        if (wifiManager?.isP2pSupported == false) {
            Log.e(TAG, "Wi-Fi Direct is not supported by the hardware or Wi-Fi is off.")
            return false
        }

        if (manager == null) {
            Log.e(TAG, "Cannot get Wi-Fi Direct system service.")
            return false
        }
        return true
    }

    @SuppressLint("MissingPermission")
    @Synchronized
    private fun connect(wifiP2pDevice: WifiP2pDevice) {
        total++
        isWifiExist = true
        if (wifiP2pDevice.status == WifiP2pDevice.AVAILABLE && !mIsConnecting.get()) {
            Log.d(TAG, "connect wifi ${wifiP2pDevice.deviceName} ${wifiP2pDevice.deviceAddress}")
            mIsConnecting.set(true)
            val config = WifiP2pConfig()
            config.deviceAddress = wifiP2pDevice.deviceAddress
            Log.d("connect analysis:", "start wifi connect::${TimeUtils.getCurrentTimeStr()}}")
            manager?.connect(mChannel, config, object : ActionListener {
                override fun onSuccess() {
                    Log.d("connect analysis:", "Connect Success::${TimeUtils.getCurrentTimeStr()}}")
                    listener.onP2pConnectSuccess()
                    startConnectCountDown()
                }

                override fun onFailure(reason: Int) {
                    Log.d(TAG, "Connect failed. Retry.")
                    mIsConnecting.set(false)
                }
            })
        }
    }


    private fun cancelConnect() {
        mChannel?.run {
            manager?.cancelConnect(this, object : ActionListener {
                override fun onSuccess() {
                    Log.d(TAG, "cancelConnect")
                }

                override fun onFailure(p0: Int) {
                    Log.e(TAG, "cancelConnect fail reason:$p0")
                }

            })
        }
        listener.onCancelConnect()
    }

    private fun registerReceiver() {
        Log.d(TAG, "register wifi receiver")
        context.registerReceiver(mReceiver, IntentFilter().apply {
            addAction(WifiManager.WIFI_STATE_CHANGED_ACTION)
            addAction(WifiP2pManager.WIFI_P2P_DISCOVERY_CHANGED_ACTION)
            addAction(WifiP2pManager.WIFI_P2P_STATE_CHANGED_ACTION)
            addAction(WifiP2pManager.WIFI_P2P_PEERS_CHANGED_ACTION)
            addAction(WifiP2pManager.WIFI_P2P_CONNECTION_CHANGED_ACTION)
            addAction(WifiP2pManager.WIFI_P2P_THIS_DEVICE_CHANGED_ACTION)
        })
    }

    private fun unregisterReceiver() {
        Log.d(TAG, "unregister wifi receiver")
        try {
            context.unregisterReceiver(mReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @SuppressLint("MissingPermission")
    private fun discoverPeers() {
        if (mIsWifiConnected) {
            return
        }
        if (mIsScanning) {
            manager?.requestPeers(mChannel, this@SoftP2pManager)
        } else {
//            Log.d("connect analysis:", "start wifi scan::${getTimeFormatter()}")
            mChannel?.let {
                manager?.discoverPeers(it, object : ActionListener {
                    override fun onSuccess() {
                        Log.d(TAG, "discoverPeers Success")
                    }

                    override fun onFailure(reasonCode: Int) {
                        Log.d(TAG, "discoverPeers failed: $reasonCode")
                    }
                })
            }
        }
    }

    @SuppressLint("MissingPermission")
    private fun stopSearch() {
        if (!mIsScanning) {
            return
        }
        manager?.stopPeerDiscovery(mChannel, object : ActionListener {
            override fun onSuccess() {
                Log.d("connect analysis:", "end wifi scan::${TimeUtils.getCurrentTimeStr()}")
                Log.d(TAG, "stopPeerDiscovery Success: ")
            }

            override fun onFailure(reasonCode: Int) {
                Log.d(TAG, "stopPeerDiscovery failed: $reasonCode")
            }
        })
    }

    private fun disconnect() {
        isWifiExist = false
        mIsConnecting.set(false)
        Log.d("connect analysis:", "disconnect wifi connect::${TimeUtils.getCurrentTimeStr()}")
        if (mIsWifiConnected) {
            mChannel?.run {
                manager?.removeGroup(this, object : ActionListener {
                    override fun onSuccess() {
                        if (autolinkStationChange(AUTOLINK_STATION_NO_CONNECT)) {
                            total = 0
                            searchExist()
//                            registerReceiver()
                        }
                        Log.d(TAG, "disconnect success")
                    }

                    override fun onFailure(reasonCode: Int) {
                        Log.d(TAG, "disconnect failed. Reason :$reasonCode")
                    }
                })
            }
        }
        mIsWifiConnected = false
    }


    override fun onChannelDisconnected() {
        Log.w(TAG, "Channel lost")
        if (manager != null && !retryChannel) {
            retryChannel = true
            mChannel = manager?.initialize(context, context.mainLooper, this)
        }
    }

    override fun onPeersAvailable(peers: WifiP2pDeviceList) {
        val findDevices = peers.deviceList
        findDevices?.run {
            find { it.isGroupOwner && it.status == WifiP2pDevice.CONNECTED }
                ?.let {
                    if (it.deviceAddress.lowercase() != mPeerAddress.lowercase()) {
                        Log.d(TAG, it.deviceAddress)
                        if (autolinkStationChange(AUTOLINK_STATION_P2P_DEVICE_ERROR)) {
                            disconnect()
                        }
                    }
                }

            find { it.deviceAddress.lowercase() == mPeerAddress.lowercase() }?.let {
                Log.d(TAG, "wifi p2p peer status:${it.status} ${mIsConnecting.get()}")
                if (mIsConnecting.get()) {
                    if (autolinkStationChange(AUTOLINK_STATION_AUTOLINK_RESTART)) {
                        if (it.isGroupOwner && it.status == WifiP2pDevice.CONNECTED) {
                            Log.d(TAG, "已经存在连接")
                            listener.connectExist()
                            mIsWifiConnected = true
                        } else {
                            Log.d(TAG, "平台端状态不正确")
                        }
                    }
                } else {
                    if (autolinkStationChange(AUTOLINK_STATION_SEARCH_SUCCESS)) {
                        connect(it)
                    }
                }
            }
        }
    }

    private fun startConnectCountDown() {
        mConnectCountDown = countDownByFlow(10, 1000, mainScope,
            onTick = {
            }, onFinish = {
                Log.d(TAG, "startConnectCountDown")
                if (!mIsWifiConnected) {
                    cancelConnect()
                }
            })
    }

    private fun searchExist() {
        if (autolinkStationChange(AUTOLINK_STATION_SEARCH_WIFI)) {
            mSearchCountDown = countDownByFlow(5, 1000, mainScope,
                onTick = {
                    Log.d(TAG, "search result:$isWifiExist")
                    if (isWifiExist) {
                        mSearchCountDown?.cancel()
                    }
                }, onFinish = {
                    //扫描10秒后没有找到wifi，请求重置平台端wifi
                    if (autolinkStationChange(AUTOLINK_STATION_SEARCH_FAILED)) {
                        Log.d(TAG, "search finish result:$isWifiExist")
                        if (!isWifiExist) {
                            listener.requestWifiInfo()
                        }
                    }
                })
        }
    }

    fun autolinkStationChange(state: Int): Boolean {
        if (autolinkStation == AUTOLINK_STATION_WIFI_START
            && (state == AUTOLINK_STATION_NO_CONNECT || state == AUTOLINK_STATION_ALREADY_CONNECTED)
        ) {
            autolinkStation = state
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_NO_CONNECT
            && state == AUTOLINK_STATION_SEARCH_WIFI
        ) {
            autolinkStation = state
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_ALREADY_CONNECTED
            && (state == AUTOLINK_STATION_WIFI_START || state == AUTOLINK_STATION_AUTOLINK_RESTART || state == AUTOLINK_STATION_P2P_DEVICE_ERROR)
        ) {
            autolinkStation = state
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_SEARCH_WIFI
            && (state == AUTOLINK_STATION_SEARCH_FAILED || state == AUTOLINK_STATION_SEARCH_SUCCESS)
        ) {
            autolinkStation = state
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_SEARCH_FAILED
            && state == AUTOLINK_STATION_WIFI_START
        ) {
            autolinkStation = state
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_SEARCH_SUCCESS
            && (state == AUTOLINK_STATION_CONNECT_SUCCESS || state == AUTOLINK_STATION_SEARCH_SUCCESS || state == AUTOLINK_STATION_CONNECT_FAILED || state == AUTOLINK_STATION_WIFI_START)
        ) {
            autolinkStation = state
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_CONNECT_FAILED
            && (state == AUTOLINK_STATION_CONNECT_SUCCESS || state == AUTOLINK_STATION_RECONNECT_FAILED)
        ) {
            autolinkStation = state
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_CONNECT_SUCCESS
            && state == AUTOLINK_STATION_WIFI_START
        ) {
            autolinkStation = state
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_RECONNECT_FAILED
            && state == AUTOLINK_STATION_WIFI_START
        ) {
            autolinkStation = state
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_AUTOLINK_RESTART
            && state == AUTOLINK_STATION_WIFI_START
        ) {
            autolinkStation = state
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_P2P_DEVICE_ERROR
            && state == AUTOLINK_STATION_NO_CONNECT
        ) {
            autolinkStation = state
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_WIFI_DISABLE
            && state == AUTOLINK_STATION_WIFI_START
        ) {
            autolinkStation = state
            return true
        }
        Log.e(TAG, " state failed return false autolinkStation:$autolinkStation,state:$state")
        return false
    }

    init {
        if (mChannel == null) {
            Log.e(TAG, "Cannot initialize Wi-Fi Direct.")
        }
        if (!initP2p()) {
            Log.e(TAG, "initP2p failed")
        }
        registerReceiver()
    }

    companion object {
        private const val TAG = "SoftP2pManager"

        /**进入app时，未获取到其他状态:0 */
        private const val AUTOLINK_STATION_WIFI_START = 0

        /**进入app时，wifi p2p未连接:1 */
        private const val AUTOLINK_STATION_NO_CONNECT = 1

        /**进入app时，wifi p2p已经连接:2*/
        private const val AUTOLINK_STATION_ALREADY_CONNECTED = 2

        /**搜索wifi，持续搜索10s:3*/
        private const val AUTOLINK_STATION_SEARCH_WIFI = 3

        /**搜索wifi重试失败，向平台请求重置:4*/
        private const val AUTOLINK_STATION_SEARCH_FAILED = 4

        /**wifi搜索成功:5*/
        private const val AUTOLINK_STATION_SEARCH_SUCCESS = 5

        /**wifi连接成功:6*/
        private const val AUTOLINK_STATION_CONNECT_SUCCESS = 6

        /**wifi连接失败，重试3次:7 */
        private const val AUTOLINK_STATION_CONNECT_FAILED = 7

        /**wifi连接重试失败，向平台请求重置:8*/
        private const val AUTOLINK_STATION_RECONNECT_FAILED = 8

        /**wifi已经连接，进入Autolink重启:9*/
        private const val AUTOLINK_STATION_AUTOLINK_RESTART = 9

        /**wifi p2p对等设备错误，移除设备:10*/
        private const val AUTOLINK_STATION_P2P_DEVICE_ERROR = 10

        /**wifi关闭:11*/
        private const val AUTOLINK_STATION_WIFI_DISABLE = 11
    }
}