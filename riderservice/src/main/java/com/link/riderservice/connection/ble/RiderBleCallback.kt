package com.link.riderservice.connection.ble

import android.bluetooth.BluetoothDevice
import com.link.riderservice.ble.data.Data

/**
 * <AUTHOR>
 * @date 2022/8/9
 */
interface RiderBleCallback {
    fun onDataReceived(device: BluetoothDevice, data: Data)
    fun onDeviceConnecting(device: BluetoothDevice)
    fun onDeviceConnected(device: BluetoothDevice)
    fun onDeviceFailedToConnect(device: BluetoothDevice, reason: Int)
    fun onDeviceReady(device: BluetoothDevice)
    fun onDeviceDisconnecting(device: BluetoothDevice)
    fun onDeviceDisconnected(device: BluetoothDevice, reason: Int)
    fun onScanResult(devices: List<BleDevice>)
    fun onRequestBt()
    fun onScanning()
    fun onScanFinish()
    fun onNeedBluetoothScanPermission()
    fun onNeedLocationPermission()
    fun onEnableNotificationFailed(device: BluetoothDevice, status: Int)
}