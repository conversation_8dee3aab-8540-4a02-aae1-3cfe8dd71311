package com.link.riderservice.connection.display.protocol.source

import android.util.Log
import com.link.riderservice.api.RiderService
import com.link.riderservice.connection.display.config.VideoConfig
import com.link.riderservice.connection.display.config.VideoPackage
import com.link.riderservice.connection.display.protocol.project.GalReceiver
import com.link.riderservice.connection.display.protocol.project.Protos
import com.link.riderservice.connection.display.protocol.project.VideoSource
import com.link.riderservice.connection.display.protocol.project.VideoSourceCallbacks

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
internal class ALVideoSource constructor(
    private val mConfig: VideoConfig?,
    private val mVideoListener: IVideoCallback,
    private val appMessageListener: GalReceiver.AppMessageListener,
    autoStart: Boolean,
    remoteHost: String?
) : ALServiceBase {

    /**
     * video source 的回调
     */
    interface IVideoCallback {
        /**
         * 视频配置
         * @param codec 视频格式
         * @param fps 帧率
         * @param w 宽
         * @param h 高
         */
        fun onVideoConfig(codec: Int, fps: Int, w: Int, h: Int)

        /**
         * 开始投屏
         */
        fun onStart()

        /**
         * 停止投屏
         */
        fun onStop()

        /**
         * 销毁投屏
         */
        fun onShutdown()
    }

    private var mSource: VideoSource? = null
    private var mCurrentSession: Int
    private val mRemoteConfigs = ArrayList<VideoRemoteConfig>()
    private var mCurrentConfigIdx: Int
    private var mOpen = false
    private var mCurrentTimeStamp: Long = 0
    private val mVideoRemoteConfig = VideoRemoteConfig()
    private var mIsLandscape = false
    private var mFocus = Protos.VIDEO_FOCUS_NATIVE
    private var mPermitStatus = PERMISSION_NO
    private var isReady :Boolean = false

    override fun destroy() {
        mOpen = false
        mSource?.destroy()
    }

    override fun create(serviceId: Int, nativeGalReceiver: Long): Boolean {
        return mSource?.create(serviceId, nativeGalReceiver) ?: false
    }

    override fun start(): Boolean {
        mCurrentTimeStamp = System.currentTimeMillis()
        return true
    }

    override val serviceType: Int
        get() = ALServiceBase.AL_SERVICE_VIDEO_SOURCE
    override val nativeInstance: Long
        get() = mSource?.nativeInstance ?: 0

    fun sendData(pkg: VideoPackage) {
        mSource?.sendData(System.currentTimeMillis(), pkg.data, pkg.size, pkg.flags)
    }


    fun stop() {
        stopVideoSession()
    }

    fun sendVideoFocusRequestNotify(dispChannel: Int, focus: Int, reason: Int) {
        mSource?.sendVideoFocusRequestNotifi(dispChannel, focus, reason)
    }

    private fun startVideoSession(isLandscape: Boolean) {
        if (mOpen) {
            stopVideoSession()
        }
        val session = ++mCurrentSession
        val width = mVideoRemoteConfig.width
        val height = mVideoRemoteConfig.height
        if (width > height && !isLandscape || width < height && isLandscape) {
            mVideoRemoteConfig.width = height
            mVideoRemoteConfig.height = width
        }

        Log.d(TAG, "startVideoSession: $mVideoRemoteConfig")
        mVideoListener.onVideoConfig(
            mVideoRemoteConfig.codec,
            mVideoRemoteConfig.frameRate,
            mVideoRemoteConfig.width,
            mVideoRemoteConfig.height
        )
        mSource?.sendStart(
            mCurrentConfigIdx,
            session,
            mVideoRemoteConfig.width,
            mVideoRemoteConfig.height
        )
        if (!mOpen) {
            mVideoListener.onStart()
        }
        mOpen = true
    }

    private fun stopVideoSession() {
        Log.d(TAG, "stopVideoSession")
        mOpen = false
        mCurrentSession = -1
        mVideoListener.onStop()
        //mVideoListener.onShutdown()
        mSource?.sendStop()
    }

    fun exitVideoSession() {
        mOpen = false
        mCurrentSession = -1
        mVideoListener.onStop()
        mVideoListener.onShutdown()
    }

    fun sendDisplayAreaChangeResponse(){
        mSource?.sendDisplayAreaChangeResponse()
    }

    private fun isSupportConfig(config: VideoRemoteConfig): Boolean {
        return if (config.codec == Protos.MEDIA_CODEC_VIDEO_H264_BP && mConfig!!.isSupportH264) {
            true
        } else config.codec != Protos.MEDIA_CODEC_VIDEO_MPEG4_ES || !mConfig!!.isSupportMpeg4
    }

    companion object {
        private const val TAG = "ALVideoSource"
        private const val PERMISSION_UNKNOWN = 0
        private const val PERMISSION_YES = 1
        private const val PERMISSION_NO = 2
    }

    init {
        mCurrentSession = -1
        mCurrentConfigIdx = -1
        val listener: VideoSourceCallbacks = object : VideoSourceCallbacks {
            override fun videoFocusNotifCallback(focus: Int, unsolicited: Boolean): Int {
                Log.d(TAG, "videoFocus: $focus--$mCurrentConfigIdx")
                if (mFocus == focus){
                    Log.d(TAG, "the same focus")
                    return Protos.STATUS_SUCCESS
                }
                mFocus = focus
                if (focus == Protos.VIDEO_FOCUS_PROJECTED) {
                    if (mCurrentConfigIdx < 0) {
                        mSource?.sendVideoFocusRequestNotifi(
                            0, Protos.VIDEO_FOCUS_NATIVE,
                            Protos.NO_VALID_VIDEO_ENCODER
                        )
                    } else if (mPermitStatus == PERMISSION_UNKNOWN) {
                        mSource?.sendVideoFocusRequestNotifi(
                            0, Protos.VIDEO_FOCUS_NATIVE,
                            Protos.WAIT_PERMISSION
                        )
                    } else if (mPermitStatus == PERMISSION_NO) {
                        mSource?.sendVideoFocusRequestNotifi(
                            0, Protos.VIDEO_FOCUS_NATIVE,
                            Protos.NO_PERMISSION
                        )
                    } else {
                        startVideoSession(mIsLandscape)
                    }
                } else {
                    if (mOpen) {
                        stopVideoSession()
                    }
                }
                return Protos.STATUS_SUCCESS
            }

            override fun displayChangeCallback(
                width: Int, height: Int, isLandscape: Boolean,
                density: Int
            ): Int {
                Log.d(TAG, "displayChangeCallback: $width--$height--$isLandscape--$density")
                val needChangeDisplay = RiderService.instance.needChangeDisplay()
                if (isReady) {//连接成功时必定创建视屏流
                    isReady = false
                }else{
                    if(!needChangeDisplay&&mVideoRemoteConfig.width == width && mVideoRemoteConfig.height == height && mIsLandscape == isLandscape){
                        return Protos.STATUS_SUCCESS//在某些情景下不创建视屏流
                    }
                }
                mVideoRemoteConfig.width = width
                mVideoRemoteConfig.height = height
                mIsLandscape = isLandscape
                mSource?.sendDisplayAreaChangeResponse()
                return Protos.STATUS_SUCCESS
            }

            override fun onChannelOpened(): Int {
                mPermitStatus = PERMISSION_YES
                mSource?.sendSetup(Protos.MEDIA_CODEC_VIDEO_H264_BP)
                return Protos.STATUS_SUCCESS
            }

            override fun discoverVideoConfigCallback(
                codec: Int,
                fps: Int,
                w: Int,
                h: Int
            ): Boolean {
                val config = VideoRemoteConfig(codec, fps, w, h)
                mRemoteConfigs.add(config)
                return isSupportConfig(config)
            }

            override fun startResponseCallback(isOK: Boolean): Int {
                return Protos.STATUS_SUCCESS
            }

            override fun stopResponseCallback(): Int {
                return Protos.STATUS_SUCCESS
            }

            override fun configCallback(
                status: Int,
                maxUnack: Int,
                prefer: IntArray,
                size: Int
            ): Int {
                for (i in 0 until size) {
                    val config = mRemoteConfigs[prefer[i]]
                    if (isSupportConfig(config)) {
                        mCurrentConfigIdx = prefer[i]
                        break
                    }
                }
                if (mCurrentConfigIdx < 0) {
                    for (i in mRemoteConfigs.indices) {
                        val config = mRemoteConfigs[i]
                        if (isSupportConfig(config)) {
                            mCurrentConfigIdx = i
                            break
                        }
                    }
                }
                val config = mRemoteConfigs[mCurrentConfigIdx]
                mVideoRemoteConfig.width = config.width
                mVideoRemoteConfig.height = config.height
                mVideoRemoteConfig.codec = config.codec
                mVideoRemoteConfig.frameRate = config.frameRate
                isReady = true
                appMessageListener.onVideoChannelReady()
                return Protos.STATUS_SUCCESS
            }

            override fun ackCallback(sessionId: Int, numFrames: Int): Int {
                return Protos.STATUS_SUCCESS
            }
        }
        mSource = VideoSource(listener, autoStart, remoteHost)
    }
}