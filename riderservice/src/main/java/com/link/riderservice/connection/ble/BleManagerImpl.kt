package com.link.riderservice.connection.ble

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothAdapter.STATE_DISCONNECTED
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothProfile.STATE_DISCONNECTING
import android.content.Context
import android.os.Build
import android.util.Log
import com.link.riderservice.BuildConfig
import com.link.riderservice.ble.BleManager
import com.link.riderservice.ble.PhyRequest
import com.link.riderservice.ble.observer.ConnectionObserver
import java.util.UUID

/**
 * <AUTHOR>
 * @date 2022/8/9
 */
internal class BleManagerImpl(
    context: Context,
    val device: BluetoothDevice,
    private val manager: BleManagerCallback
) : BleManager(context) {
    private var myCharacteristic: BluetoothGattCharacteristic? = null

    override fun isRequiredServiceSupported(gatt: BluetoothGatt): Boolean {
        myCharacteristic = gatt.getService(SERVICE_UUID)?.getCharacteristic(CHARACTERISTICS)
        return true
    }

    override fun onServicesInvalidated() {
        myCharacteristic = null
    }

    override fun initialize() {
        requestMtu(MTU).enqueue()
        setNotificationCallback(myCharacteristic).with { device, data ->
            manager.onDataReceived(device, data)
        }
        beginAtomicRequestQueue()
            .add(enableNotifications(myCharacteristic)
                .fail { device: BluetoothDevice, status: Int ->
                    Log.d(TAG, "Could not subscribe: $status")
                    manager.onEnableNotificationFailed(device, status)
                }
            )
            .done { Log.d(TAG, "Target initialized") }
            .enqueue()
    }

    override fun getMinLogPriority(): Int {
        return if (BuildConfig.DEBUG) Log.VERBOSE else Log.INFO
    }

    override fun log(priority: Int, message: String) {
        Log.d(TAG, message)
    }

    init {
        connectionObserver = object : ConnectionObserver {
            override fun onDeviceConnecting(device: BluetoothDevice) {
                manager.onDeviceConnecting(device)
            }

            override fun onDeviceConnected(device: BluetoothDevice) {
                manager.onDeviceConnected(device)
            }

            override fun onDeviceFailedToConnect(device: BluetoothDevice, reason: Int) {
                manager.onDeviceFailedToConnect(device, reason)
            }

            override fun onDeviceReady(device: BluetoothDevice) {
                manager.onDeviceReady(device)
            }

            override fun onDeviceDisconnecting(device: BluetoothDevice) {
                manager.onDeviceDisconnecting(device)
            }

            override fun onDeviceDisconnected(device: BluetoothDevice, reason: Int) {
                manager.onDeviceDisconnected(device, reason)
            }

        }
    }

    fun write(value: ByteArray) {
        writeCharacteristic(
            myCharacteristic,
            value,
            BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT
        ).fail { device, status ->
            manager.onWriteRequestFailed(device, status)
        }.enqueue()
    }

    fun connect(timeout: Long = 20_000) {
        if (!isConnected) {
            val adapter = BluetoothAdapter.getDefaultAdapter()
            var phy = PhyRequest.PHY_LE_1M_MASK
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                if (adapter.isLe2MPhySupported) {
                    Log.d(TAG, "2M PHY supported")
                    phy = phy or PhyRequest.PHY_LE_2M_MASK
                }

                if (adapter.isLeCodedPhySupported) {
                    Log.d(TAG, "Coded PHY supported")
                    phy = phy or PhyRequest.PHY_LE_CODED_MASK
                }
            }

            connect(device)
                .usePreferredPhy(phy)
                .retry(5, 500)
                .fail { _, status ->
                    Log.e(TAG, "----connect request fail----status=$status")
                }
                .timeout(timeout)
                .enqueue()
        }
    }

    fun release() {
        if (connectionState == STATE_DISCONNECTED || connectionState == STATE_DISCONNECTING) {
            close()
            return
        }
        disconnect().enqueue()
    }

    companion object {
        private const val MTU = 256
        private val SERVICE_UUID: UUID = UUID.fromString("00001819-0000-1000-8000-00805f9b34fb")
        private val CHARACTERISTICS: UUID = UUID.fromString("00002a37-0000-1000-8000-00805f9b34fb")
        private const val TAG = "RiderBleManager"
    }
}