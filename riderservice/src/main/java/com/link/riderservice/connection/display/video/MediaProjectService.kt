package com.link.riderservice.connection.display.video

import android.content.Context
import android.graphics.SurfaceTexture
import android.graphics.SurfaceTexture.OnFrameAvailableListener
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.MediaCodec
import android.media.MediaCodecInfo
import android.media.MediaFormat
import android.media.MediaFormat.MIMETYPE_VIDEO_AVC
import android.media.projection.MediaProjection
import android.opengl.EGLContext
import android.opengl.GLES20
import android.os.Build
import android.os.Handler
import android.os.HandlerThread
import android.util.Log
import android.view.Display
import android.view.Surface
import com.link.riderservice.api.dto.NaviMode
import com.link.riderservice.connection.display.config.VideoPackage
import com.link.riderservice.connection.display.protocol.source.ALVideoSource
import com.link.riderservice.mirror.glutils.EglTask
import com.link.riderservice.mirror.glutils.FullFrameRect
import com.link.riderservice.mirror.glutils.Texture2dProgram
import com.link.riderservice.mirror.glutils.WindowSurface
import com.link.riderservice.utils.TimeUtils
import java.lang.Integer.max
import java.lang.Integer.min
import kotlin.math.roundToInt

internal class MediaProjectService(
    val context: Context,
    private val onDisplayInitialized: (display: Display) -> Unit,
    private val onDisplayReleased: (display: Display) -> Unit,
    private val onRequestMediaProjection: () -> Unit,
    private val onMirrorStart: () -> Unit,
    private val onMirrorStop: () -> Unit,
    private val onPresentationStart: () -> Unit = {},
    private val onPresentationStop: () -> Unit = {},
    private val sendFrame: (pkg: VideoPackage) -> Unit
) : ALVideoSource.IVideoCallback {
    private var mCodec: MediaCodec? = null
    private var mInputSurface: Surface? = null
    private val mPackage = VideoPackage()
    private val codecHandlerThread: HandlerThread = HandlerThread(TAG)
    private var backgroundHandler: Handler
    private var mPresentationVirtualDisplay: VirtualDisplay? = null
    private var mMediaProjection: MediaProjection? = null
    private val displayManager: DisplayManager =
        context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
    private var mNaviMode: NaviMode = NaviMode.Default
    private var mWidth: Int = 0
    private var mHeight: Int = 0
    private var mCaptureThread: Thread? = null
    private val mSync = Object()
    private var mIsRecording = false
    private var mRequestDraw = false
    private var mRequestEncode = false
    private var first = true
    //设置 MediaCodec 的参数，默认是 AVC 格式 (H264)
    private val format = MediaFormat().apply {
        setString(MediaFormat.KEY_MIME, MIMETYPE_VIDEO_AVC)
        setInteger(
            MediaFormat.KEY_COLOR_FORMAT,
            MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface
        )
        setInteger(MediaFormat.KEY_BIT_RATE, BITRATE)
        setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, I_FRAME_INTERVAL)
    }

    /**
     * 获得编码后的数据
     * @param codec 当前的Codec
     * @param index 编码后的数据在Codec中的索引
     * @param info  编码后的数据的信息
     */
    private fun dequeueRenderFrame(codec: MediaCodec, index: Int, info: MediaCodec.BufferInfo) {
        try {
            codec.getOutputBuffer(index)?.let { buffer ->
                buffer.position(info.offset)
                buffer.limit(info.offset + info.size)
                mPackage.apply {
                    data = ByteArray(info.size)
                    size = info.size
                    flags = info.flags
                }
                buffer.get(mPackage.data, 0, info.size)
                sendFrame(mPackage)
                codec.releaseOutputBuffer(index, false)
            }
        } catch (e: IllegalStateException) {
            Log.e(TAG, "onOutputBufferAvailable", e)
        }
    }

    private val codecCallback = object : MediaCodec.Callback() {
        override fun onInputBufferAvailable(codec: MediaCodec, index: Int) {
            //do nothing
        }

        override fun onOutputBufferAvailable(
            codec: MediaCodec, index: Int, info: MediaCodec.BufferInfo
        ) {
            if(first) {
                first = false
                Log.d("connect analysis:", "start autolink stream::${TimeUtils.getCurrentTimeStr()}")
            }

            dequeueRenderFrame(codec, index, info)
        }

        override fun onError(
            codec: MediaCodec, e: MediaCodec.CodecException
        ) {
            //do nothing
        }

        override fun onOutputFormatChanged(
            codec: MediaCodec, format: MediaFormat
        ) {
            //do nothing
        }
    }

    fun setNaviMode(naviMode: NaviMode) {
        Log.d(TAG, "setNaviMode $naviMode")
        mNaviMode = naviMode
    }

    fun setMediaProjection(mediaProjection: MediaProjection) {
        mMediaProjection = mediaProjection
        startRender()
    }

    private fun initVirtualDisplay() {


    }

    private fun initRealVirtualDisplay() {
        if (mMediaProjection == null) {
            onRequestMediaProjection()
            return
        }


    }

    override fun onVideoConfig(codec: Int, fps: Int, w: Int, h: Int) {
        mWidth = w
        mHeight = h
        format.run {
            setInteger(MediaFormat.KEY_WIDTH, mWidth)
            setInteger(MediaFormat.KEY_HEIGHT, mHeight)
            setInteger(MediaFormat.KEY_FRAME_RATE, fps)
            setInteger(MediaFormat.KEY_CAPTURE_RATE, fps)
            setInteger(MediaFormat.KEY_REPEAT_PREVIOUS_FRAME_AFTER, 1000000 / fps)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                setInteger(MediaFormat.KEY_PROFILE, MediaCodecInfo.CodecProfileLevel.AVCProfileHigh)
                setInteger(MediaFormat.KEY_LEVEL, MediaCodecInfo.CodecProfileLevel.AVCLevel41)
            }
        }
        try {
            if (mCodec == null) {
                mCodec = MediaCodec.createEncoderByType("video/avc")
            }
            mCodec?.setCallback(codecCallback, backgroundHandler)
            mCodec?.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE)
            mInputSurface = mCodec?.createInputSurface()
            if (mNaviMode == NaviMode.MirrorNAVI) {
                initRealVirtualDisplay()
            } else {
                initVirtualDisplay()
            }
        } catch (e: Exception) {
            Log.e(TAG, "initVirtualDisplay failed", e)
        }
    }

    override fun onStart() {
        Log.d(TAG, "onStart $mNaviMode")
        mCodec?.start()
        startRender()
    }

    private fun startRender() {
        if (mNaviMode == NaviMode.MirrorNAVI && mMediaProjection == null) {
            Log.w(TAG, "startRender: mMediaProjection is null")
            return
        }
        if (mCaptureThread != null && mCaptureThread?.isAlive == true) {
            try {
                mCaptureThread?.join()
            } catch (e: InterruptedException) {
                e.printStackTrace()
            }
            mCaptureThread = null
        }
        mIsRecording = true
        val screenCaptureTask = DrawTask(null, 0)
        mCaptureThread = Thread(screenCaptureTask, "ScreenCaptureThread")
        mCaptureThread?.start()
    }

    override fun onStop() {
        Log.d(TAG, "onStop mNaviMode=$mNaviMode")
        mCodec?.stop()
        mCodec?.release()
        mCodec = null
        mInputSurface?.release()
        mInputSurface = null
        synchronized(mSync) {
            mIsRecording = false
            mSync.notifyAll()
        }
        mPresentationVirtualDisplay?.let {
            onDisplayReleased(it.display)
        }
        if (mCaptureThread != null) {
            mCaptureThread?.interrupt()
        }
        if (!(mNaviMode == NaviMode.LockScreenNavi||mNaviMode == NaviMode.MirrorNAVI)) {
            if (mMediaProjection != null) {
                mMediaProjection?.stop()
                mMediaProjection = null
                onMirrorStop()
            } else {
                onPresentationStop()
            }
        }
    }

    override fun onShutdown() {
        Log.d(TAG, "onShutdown")
        onStop()
        if (mMediaProjection != null) {
            mMediaProjection?.stop()
            mMediaProjection = null
            onMirrorStop()
        }
    }

    private fun getDensityDpi(): Int {
        val max = max(mWidth, mHeight)
        val min = min(mWidth, mHeight)
        if ((max == 1024 && min == 600) || (max == 1024 && min == 496)) {
            return 256f.roundToInt()
        }

        if (max == 1280 && min == 720) {
            return 320f.roundToInt()
        }

        if (max == 1920 && min == 1080) {
            return 480f.roundToInt()
        }

        if (max == 800 && min == 480) {
            return 240f.roundToInt()
        }

        val scale = max / min.toFloat()
        if (scale < 1.34f) {
            return 256f.roundToInt()
        }
        if (scale > 2.0f) {
            return (((min * 160.0f) / 640.0f) * 1.6f).roundToInt()
        }
        return (((min * 160.0f) / 720.0f) * 1.6f).roundToInt()
    }

    private inner class DrawTask(sharedContext: EGLContext?, flags: Int) :
        EglTask(sharedContext, flags) {
        private var mIntervals: Long = 0
        private var mTexId = 0
        private var mSourceTexture: SurfaceTexture? = null
        private var mSourceSurface: Surface? = null
        private var mEncoderSurface: WindowSurface? = null
        private var mDrawer: FullFrameRect? = null
        private val mTexMatrix = FloatArray(16)
        private var mLastFrameTime = System.currentTimeMillis()

        override fun onStart() {
            Log.d(TAG, "onStart $mNaviMode")
            mDrawer = FullFrameRect(Texture2dProgram(Texture2dProgram.ProgramType.TEXTURE_EXT))
            mTexId = mDrawer!!.createTextureObject()
            mSourceTexture = SurfaceTexture(mTexId, false)
            mSourceTexture!!.setDefaultBufferSize(mWidth, mHeight)
            mSourceSurface = Surface(mSourceTexture)
            mEncoderSurface = WindowSurface(eglCore, mInputSurface)
            mSourceTexture!!.setOnFrameAvailableListener(
                mOnFrameAvailableListener,
                backgroundHandler
            )
            mIntervals = (1000f / 30).toLong()
            if (mNaviMode == NaviMode.MirrorNAVI) {
                mPresentationVirtualDisplay = mMediaProjection?.createVirtualDisplay(
                    "Mirror Display", mWidth,
                    mHeight, getDensityDpi(), DisplayManager.VIRTUAL_DISPLAY_FLAG_PUBLIC,
                    mSourceSurface, null, null
                )
                onMirrorStart()
            } else {
                mPresentationVirtualDisplay = displayManager.createVirtualDisplay(
                    "ext-display",
                    mWidth,
                    mHeight,
                    getDensityDpi(),
                    mSourceSurface,
                    DisplayManager.VIRTUAL_DISPLAY_FLAG_PRESENTATION //如果是录屏，需要修改这里
                )
                mPresentationVirtualDisplay?.display?.let {
                    onDisplayInitialized(it)
                }
                onPresentationStart()
            }
            queueEvent(mDrawTask)
        }


        override fun onStop() {
            Log.d(TAG, "onStop")
            mDrawer?.release()
            mDrawer = null

            mSourceSurface?.release()
            mSourceSurface = null

            mSourceTexture?.release()
            mSourceTexture = null

            mEncoderSurface?.release()
            mEncoderSurface = null

            makeCurrent()

            mPresentationVirtualDisplay?.release()
            mPresentationVirtualDisplay = null
        }

        override fun onError(e: java.lang.Exception?): Boolean {
            Log.e(TAG, "onError $e")
            return true
        }

        override fun processRequest(request: Int, arg1: Int, arg2: Any?): Boolean {
            return false
        }

        private val mOnFrameAvailableListener =
            OnFrameAvailableListener { _: SurfaceTexture? ->
                if (mIsRecording) {
                    synchronized(mSync) {
                        mRequestDraw = true
                        mSync.notifyAll()
                    }
                }
            }
        private val mDrawTask: Runnable = object : Runnable {
            override fun run() {
                var localRequestDraw: Boolean
                synchronized(mSync) {
                    localRequestDraw = mRequestDraw
                    mRequestDraw = false
                    if (!localRequestDraw) {
                        try {
                            mSync.wait(mIntervals)
                            localRequestDraw = mRequestDraw
                            mRequestDraw = false
                        } catch (e: InterruptedException) {
                            releaseSelf()
                            return
                        }
                    }
                }
                if (mIsRecording) {
                    if (localRequestDraw) {
                        mSourceTexture!!.updateTexImage()
                        mSourceTexture!!.getTransformMatrix(mTexMatrix)
                        mRequestEncode = true
                    }
                    val nowTime = System.currentTimeMillis()
                    if (nowTime - mLastFrameTime > mIntervals) {
                        mLastFrameTime = nowTime
                        if (mRequestEncode) {
                            mRequestEncode = false
                            mEncoderSurface!!.makeCurrent()
                            mDrawer!!.drawFrame(mTexId, mTexMatrix)
                            mEncoderSurface!!.swapBuffers()
                        }
                    }
                    makeCurrent()
                    if (localRequestDraw) {
                        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT)
                        GLES20.glFlush()
                    }
                    queueEvent(this)
                } else {
                    releaseSelf()
                }
            }
        }
    }

    init {
        codecHandlerThread.start()
        backgroundHandler = Handler(codecHandlerThread.looper)
    }

    companion object {
        private const val TAG = "MediaProjectService"
        private const val BITRATE = 6 * 1024 * 1024
        private const val I_FRAME_INTERVAL = 1
    }

}