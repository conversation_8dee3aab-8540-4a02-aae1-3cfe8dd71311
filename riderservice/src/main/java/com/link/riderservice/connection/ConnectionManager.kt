package com.link.riderservice.connection

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.content.Context
import android.media.projection.MediaProjection
import android.util.Log
import android.view.Display
import com.link.riderservice.R
import com.link.riderservice.api.RiderService
import com.link.riderservice.api.RiderServiceCallback
import com.link.riderservice.api.dto.BleStatus
import com.link.riderservice.api.dto.Connection
import com.link.riderservice.api.dto.NaviMode
import com.link.riderservice.api.dto.RiderServiceConfig
import com.link.riderservice.api.dto.WifiStatus
import com.link.riderservice.authorize.data.repository.AuthorizeRepositoryImpl
import com.link.riderservice.authorize.data.source.remote.AuthorizeRemoteSourceImpl
import com.link.riderservice.ble.data.Data
import com.link.riderservice.connection.ble.BleDevice
import com.link.riderservice.connection.ble.RiderBleCallback
import com.link.riderservice.connection.ble.RiderBleManager
import com.link.riderservice.connection.display.DisplayNaviCallback
import com.link.riderservice.connection.display.DisplayNaviManager
import com.link.riderservice.ext.collectWithScope
import com.link.riderservice.ext.setState
import com.link.riderservice.inject.ModuleInject
import com.link.riderservice.message.toByteArray
import com.link.riderservice.protobuf.RiderProtocol
import com.link.riderservice.utils.Platform
import com.link.riderservice.utils.TimeUtils
import com.link.riderservice.utils.mainScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.retry
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference
import java.util.Timer
import java.util.TimerTask

/**
 * <AUTHOR>
 * @date 2022/8/25
 * @desc 连接管理类
 */
internal class ConnectionManager {
    private val _connectionStatus = MutableStateFlow(Connection())
    private val connectionStatus = _connectionStatus.asStateFlow()
    private var mDisplayNaviManager: DisplayNaviManager = DisplayNaviManager(requestWifiInfo = {
        requestWifiInfo()
    }, onWifiState = { opened ->
        if (!opened && connectionStatus.value.btStatus is BleStatus.DeviceConnected) {
            mCallbacks.forEach {
                it.get()?.onDialogShow(mApplication.getString(R.string.error_title), mApplication.getString(R.string.open_wifi))
            }
        }
        mCallbacks.forEach {
            it.get()?.onWifiState(opened)
        }
    })
    private var mTimer: Timer? = null
    private var mIsManual: Boolean = false
    private val mAuthorizeRepository = AuthorizeRepositoryImpl(AuthorizeRemoteSourceImpl())
    private val mCallbacks: MutableList<WeakReference<RiderServiceCallback>> = mutableListOf()
    private var mBleAdress: String? = null
    private var mBleName: String? = null
    private val mApplication by lazy { RiderService.instance.getApplication() }

    @Synchronized
    fun addCallback(callback: RiderServiceCallback) {
        mCallbacks.add(WeakReference(callback))
    }

    @Synchronized
    fun removeCallback(callback: RiderServiceCallback) {
        mCallbacks.removeIf { it.get() == callback }
    }

    private val mBleCallback = object : RiderBleCallback {
        override fun onDataReceived(device: BluetoothDevice, data: Data) {
            data.value?.let { buf ->
                ModuleInject.messageManager.enqueueIncoming(buf, buf.size)
            }
        }

        override fun onDeviceConnecting(device: BluetoothDevice) {
            Log.d(TAG, "onDeviceConnecting: $device ")
            _connectionStatus.setState { copy(btStatus = BleStatus.DeviceConnecting(device)) }
        }

        @SuppressLint("MissingPermission")
        override fun onDeviceConnected(device: BluetoothDevice) {
            Log.d(TAG, "onDeviceConnected")
            mBleAdress = device.address
            mBleName = device.name
            _connectionStatus.setState { copy(btStatus = BleStatus.DeviceConnected(device)) }
        }

        override fun onDeviceFailedToConnect(device: BluetoothDevice, reason: Int) {
            Log.d(TAG, "onDeviceFailedToConnect")
            _connectionStatus.setState {
                copy(btStatus = BleStatus.DeviceFailedToConnect(device, reason))
            }
        }

        override fun onDeviceReady(device: BluetoothDevice) {
            Log.d(TAG, "onDeviceReady")
            loopWrite()
            mIsManual = false
        }

        override fun onDeviceDisconnecting(device: BluetoothDevice) {
        }

        override fun onDeviceDisconnected(device: BluetoothDevice, reason: Int) {
            Log.d(TAG, "onDeviceDisconnected: $reason")
            cancelConnectWithTimeout()
            ModuleInject.messageManager.clearMessage()
            RiderBleManager.instance.release(mIsManual)
            _connectionStatus.setState {
                copy(btStatus = BleStatus.DeviceDisconnected(device, reason))
            }
            if (!mIsManual) {
                startScan()
            }
        }

        override fun onScanResult(devices: List<BleDevice>) {
            mCallbacks.forEach {
                it.get()?.onScanResult(devices)
            }
        }

        override fun onRequestBt() {
            mCallbacks.forEach {
                it.get()?.onRequestOpenBluetooth()
            }
        }

        override fun onScanning() {
            mCallbacks.forEach {
                it.get()?.onScanning()
            }
        }

        override fun onScanFinish() {
            mCallbacks.forEach {
                it.get()?.onScanFinish()
            }
        }

        override fun onNeedBluetoothScanPermission() {
            mCallbacks.forEach {
                it.get()?.onNeedBluetoothScanPermission()
            }
        }

        override fun onNeedLocationPermission() {
            mCallbacks.forEach {
                it.get()?.onNeedLocationPermission()
            }
        }

        override fun onEnableNotificationFailed(device: BluetoothDevice, status: Int) {
            mCallbacks.forEach {
                it.get()?.onEnableNotificationFailed(device, status)
            }
            disconnect()
        }
    }

    private val mDisplayNaviCallback = object : DisplayNaviCallback {
        override fun onDeviceConnected() {
            Log.d(TAG, "wifi socket connected")
            mDisplayNaviManager.start()
            _connectionStatus.setState { copy(wifiStatus = WifiStatus.DeviceConnected) }
        }

        override fun onDeviceDisconnected() {
            Log.d(TAG, "wifi socket disconnected")
            _connectionStatus.setState { copy(wifiStatus = WifiStatus.DeviceDisconnected) }
            requestWifiInfo()
        }

        override fun onDisplayInitialized(display: Display) {
            mCallbacks.forEach {
                it.get()?.onDisplayInitialized(display)
            }
        }

        override fun onDisplayReleased(display: Display) {
            mCallbacks.forEach {
                it.get()?.onDisplayReleased(display)
            }
        }

        override fun onVideoChannelReady() {
            mCallbacks.forEach {
                it.get()?.onVideoChannelReady()
            }
        }

        override fun onRequestMediaProjection() {
            mCallbacks.forEach {
                it.get()?.onRequestMediaProjection()
            }
        }

        override fun onMirrorStart() {
            mCallbacks.forEach {
                it.get()?.onMirrorStart()
            }
        }

        override fun onMirrorStop() {
            mCallbacks.forEach {
                it.get()?.onMirrorStop()
            }
        }

    }

    private fun loopWrite() {
        mainScope.launch(Dispatchers.IO) {
            while (RiderBleManager.instance.isConnected()) {
                ModuleInject.messageManager.dequeueOutgoing()?.let {
                    RiderBleManager.instance.write(it.toByteArray())
                }
            }
            RiderBleManager.instance.release(false)
        }
    }

    fun showDialog(title: String, message: String) {
        mCallbacks.forEach {
            it.get()?.onDialogShow(title, message)
        }
    }

    fun release() {
        disconnect()
        _connectionStatus.setState { copy(wifiStatus = WifiStatus.IDLE, btStatus = BleStatus.IDLE) }
        mDisplayNaviManager.removeCallback(mDisplayNaviCallback)
        RiderBleManager.instance.release()
    }

    /**
     * 连接 BLE 设备
     */
    fun connectBle(device: BleDevice, context: Context) {
        RiderBleManager.instance.connect(device, context)
    }

    /**
     * 断开连接
     */
    @Synchronized
    fun disconnect(isManual: Boolean = true) {
        mIsManual = isManual
        ModuleInject.messageManager.clearMessage()
        RiderBleManager.instance.release()
        mDisplayNaviManager.sendByeByeRequest()
        mDisplayNaviManager.release()
    }

    fun closeConnect() {
        ModuleInject.messageManager.clearMessage()
        RiderBleManager.instance.closeConnect()
    }

    /**
     * 搜索并连接 WLAN 直连
     * @param address 平台 MAC 地址
     * @param port 端口 默认 30512
     */
    fun startSearchWifiAndConnect(context: Context, address: String, port: Int) {
        if (_connectionStatus.value.wifiStatus is WifiStatus.DeviceConnected) {
            Log.d(TAG, "wifi already connected")
            return
        }
        mDisplayNaviManager.startSearchWifiAndConnect(
            context,
            mBleName.toString(),
            mBleAdress.toString(),
            address,
            port
        )
    }

    /**
     * Autolink启动成功
     */
    fun startAutolinkServiceSuccess() {
        mDisplayNaviManager.setRequestNotificationSuccess()
    }

    /**
     * 开始投屏
     * @return 是否成功
     */
    fun startScreenProjection(): Boolean {
        return mDisplayNaviManager.startScreenProjection()
    }

    /**
     * 停止投屏
     * @return 是否成功
     */
    fun stopScreenProjection(): Boolean {
        return mDisplayNaviManager.stopScreenProjection()
    }

    /**
     * 发送手机屏幕信息给平台
     * @param isLandscape 是否横屏
     * @param rotation 屏幕旋转角度
     */
    fun sendOrientation(isLandscape: Int, rotation: Int = Platform.NORMAL) {
        mDisplayNaviManager.sendOrientation(isLandscape, rotation)
    }

    /**
     * 请求平台 Wifi 信息
     * @param isReset 是否重置平台 wifi
     */
    fun requestWifiInfo(isReset: Boolean = true) {
        Log.d(TAG, "requestWifiInfo ${_connectionStatus.value.btStatus}")
        if (_connectionStatus.value.btStatus is BleStatus.DeviceConnected) {
            Log.d("connect analysis:", "wifi info request::${TimeUtils.getCurrentTimeStr()}")
            ModuleInject.messageManager.queueOutgoing(
                RiderProtocol.NaviMessageId.MSG_WIFI_INFO_REQUEST_VALUE,
                RiderProtocol.WifiInfoRequest
                    .newBuilder()
                    .setWifiMode(RiderProtocol.WifiMode.WIFI_P2P)
                    .setIsResetWifi(isReset).build()
            )
        }
    }

    fun setNaviMode(naviMode: NaviMode) {
        mDisplayNaviManager.setNaviMode(naviMode)
    }

    fun setMediaProjection(mediaProjection: MediaProjection) {
        mDisplayNaviManager.setMediaProjection(mediaProjection)
    }

    private fun requestProtocolVersion() {
        if (_connectionStatus.value.btStatus is BleStatus.DeviceConnected) {
            ModuleInject.messageManager.queueOutgoing(
                RiderProtocol.NaviMessageId.MSG_PROTOCOL_VERSION_REQUEST_VALUE,
                RiderProtocol.ProtocolVerRequest.newBuilder().build()
            )
            connectWithTimeout()
        }
    }

    private fun connectWithTimeout() {
        if (mTimer == null) {
            mTimer = Timer()
        }
        mTimer?.schedule(object : TimerTask() {
            override fun run() {
                protocolError(mApplication.getString(R.string.connect_timeout))
            }
        }, 3000)
    }

    private fun cancelConnectWithTimeout() {
        mTimer?.cancel()
        mTimer?.purge()
        mTimer = null
    }

    private fun protocolError(message: String, error: Int = 0) {
        Log.e(TAG, message)
        showDialog(mApplication.getString(R.string.error_title), message)
        if (error == NETWORK_ERROR) {
            disconnect(false)
        } else {
            disconnect()
        }
    }

    /**
     * BLE 设备是否连接
     * @return 是否连接
     */
    fun isBleConnected(): Boolean {
        return RiderBleManager.instance.isConnected()
    }

    /**
     * 匹配版本
     * @param version 版本
     */
    fun checkVersion(version: RiderProtocol.ProtocolVerNotification) {
        cancelConnectWithTimeout()
        if (version.major <= RiderProtocol.ProtocolVersion.MAJOR_VERSION_VALUE) {
            if (RiderBleManager.instance.isConfigConnect() && !RiderService.instance.getOnBleItemClick()) {
                val sharedPref = RiderService.instance.getApplication()
                    .getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
                val address = sharedPref.getString("wifi_address", "")
                val port = sharedPref.getInt("wifi_port", 0)
                if (address != null && port != 0) {
                    if (_connectionStatus.value.wifiStatus is WifiStatus.DeviceConnected) {
                        Log.d(TAG, "wifi already connected")
                        return
                    }
                    mDisplayNaviManager.startSearchWifiAndConnect(
                        RiderService.instance.getApplication(),
                        mBleName.toString(),
                        mBleAdress.toString(),
                        address,
                        port
                    )
                } else {
                    showDialog(mApplication.getString(R.string.error_title), mApplication.getString(R.string.config_error))
                }
            } else {
                requestWifiInfo(false)
            }
        } else {
            protocolError(mApplication.getString(R.string.version_error))
        }
    }

    /**
     * 请求服务器激活
     * @param sdkKey 平台 SDK 的 key
     * @param macAddr 平台的MAC地址
     * @param timestamp 时间戳
     * @param sign 平台签名
     */
    fun requestActivate(
        sdkKey: String, macAddr: String, timestamp: String, sign: String
    ) {
        mainScope.launch(Dispatchers.IO) {
            val activeNotification = RiderProtocol.ActivateNotification.newBuilder()
            try {
                val result = mAuthorizeRepository.requestActivateStatus(
                    key = sdkKey, macAddr, timestamp, sign
                )
                activeNotification.result = result.getOrThrow().status
                activeNotification.uuid = result.getOrThrow().uuid
            } catch (e: Exception) {
                activeNotification.result = NETWORK_ERROR
            }
            ModuleInject.messageManager.queueOutgoing(
                RiderProtocol.NaviMessageId.MSG_ACTIVE_NOTIFICATION_VALUE,
                activeNotification.build()
            )
            if (activeNotification.result != SUCCESS) {
                protocolError(mApplication.getString(R.string.activate_failed))
            }
        }
    }

    /**
     * 请求服务器验证
     * @param productkey 产品 Key
     * @param macAddr 平台的MAC地址
     * @param uuid 产品唯一ID
     * @param timestamp 时间戳
     * @param licenseSign 证书签名
     * @param sign 平台签名
     */
    fun requestVerify(
        productkey: String,
        macAddr: String,
        uuid: String,
        timestamp: String,
        licenseSign: String,
        sign: String
    ) {
        val complianceNotification = RiderProtocol.ComplianceNotification.newBuilder()
        mainScope.launch {
            flow {
                emit(
                    mAuthorizeRepository.requestCheckStatus(
                        productkey, macAddr, uuid, timestamp, licenseSign, sign
                    )
                )
            }
                .retry(3) {
                    if (it is Exception) {
                        delay(1000)
                        true
                    } else {
                        false
                    }
                }
                .catch {
                    complianceNotification.result = NETWORK_ERROR
                    Log.e(TAG, "requestVerify: $it")
                }.flowOn(Dispatchers.IO)
                .collect {
                    complianceNotification.result = it.getOrThrow().status
                }
            ModuleInject.messageManager.queueOutgoing(
                RiderProtocol.NaviMessageId.MSG_COMPLICANCE_NOTIFICATION_VALUE,
                complianceNotification.build()
            )
            if (complianceNotification.result != SUCCESS) {
                if (complianceNotification.result == NETWORK_ERROR) {
                    protocolError(mApplication.getString(R.string.network_error), NETWORK_ERROR)
                } else {
                    protocolError(mApplication.getString(R.string.verify_failed))
                }
            } else {
                requestProtocolVersion()
                mCallbacks.forEach {
                    it.get()?.onClusterReady()
                }
            }
        }
    }

    /**
     * 开启设备扫描
     */
    fun startScan() {
        RiderBleManager.instance.startScan()
    }

    /**
     * 停止设置扫描
     */
    fun stopScan() {
        RiderBleManager.instance.stopScan()
    }

    /**
     * 初始化
     * @param context
     */
    fun init(context: Context?) {
        RiderBleManager.instance.addCallback(mBleCallback)
        context?.let {
            RiderBleManager.instance.registerBroadcastReceivers(context)
        }
    }

    /**
     * 销毁并释放资源
     * @param context
     */
    fun destroy(context: Context) {
        RiderBleManager.instance.stopScan()
        RiderBleManager.instance.removeCallback(mBleCallback)
        RiderBleManager.instance.unregisterBroadcastReceivers(context)
    }

    /**
     * 获取连接信息
     * @return 连接信息
     * @see Connection
     */
    fun getConnectStatus(): Connection {
        return _connectionStatus.value
    }

    /**
     * 导航模式发改变
     * @param mode 模式
     * @see NaviMode
     */
    fun sendNaviModeChange(mode: NaviMode) {
        mCallbacks.forEach {
            it.get()?.onNaviModeChange(mode)
        }
    }

    fun sendNaviModeChangeResponse(naviMode: RiderProtocol.NaviMode, ready: Boolean) {
        mCallbacks.forEach {
            it.get()?.onNaviModeChangeResponse(getNaviMode(naviMode), ready)
        }
    }

    fun sendRiderServiceConfigChange(riderServiceConfig: RiderServiceConfig) {
        mCallbacks.forEach { callback ->
            callback.get()?.onConfigChange(riderServiceConfig)
        }
    }

    fun getCurrentConnectDevice(): BleDevice? {
        return RiderBleManager.instance.getCurrentConnectDevice()
    }

    fun updateWeatherInfo() {
        mCallbacks.forEach { callback ->
            callback.get()?.onRequestWeatherInfo()
        }
    }

    fun naviversionResponse(version: String) {
        mCallbacks.forEach { callback ->
            callback.get()?.naviversionResponse(version)
        }
    }

    fun sendNaviModeStartResponse(naviMode: RiderProtocol.NaviMode) {
        mCallbacks.forEach { callback ->
            callback.get()?.onNaviModeStartResponse(getNaviMode(naviMode))
        }
    }

    fun sendNaviModeStopResponse(naviMode: RiderProtocol.NaviMode) {
        mCallbacks.forEach { callback ->
            callback.get()?.onNaviModeStopResponse(getNaviMode(naviMode))
        }
    }

    private fun getNaviMode(naviMode: RiderProtocol.NaviMode): NaviMode {
        return when (naviMode) {
            RiderProtocol.NaviMode.DEFAULT_NAVI -> NaviMode.Default
            RiderProtocol.NaviMode.SIMPLE_NAVI -> NaviMode.SimpleNavi
            RiderProtocol.NaviMode.SCREEN_NAVI -> NaviMode.ScreenNavi
            RiderProtocol.NaviMode.MIRROR_NAVI -> NaviMode.MirrorNAVI
            RiderProtocol.NaviMode.CRUISE_NAVI -> NaviMode.CruiseNAVI
            RiderProtocol.NaviMode.LOCK_SCREEN_NAVI -> NaviMode.LockScreenNavi
            RiderProtocol.NaviMode.NO_NAVI -> NaviMode.NoNavi
            else -> NaviMode.NoNavi
        }
    }

    fun disconnectWifi() {
        mDisplayNaviManager.shutdown()
    }

    fun changeMap(type: Int) {
        mCallbacks.forEach { callback ->
            Log.e(TAG, "changeMap: callback")
            callback.get()?.changeMap(type)
        }
    }

    fun requestLockScreenDisplay() {
        mDisplayNaviManager.requestLockScreenDisplay()
    }

    init {
        connectionStatus.collectWithScope(mainScope) { connection ->
            mCallbacks.forEach {
                it.get()?.onConnectStatusChange(connection)
            }
        }
        mDisplayNaviManager.addCallback(mDisplayNaviCallback)
    }


    companion object {
        private const val TAG = "ConnectionManager"
        private const val NETWORK_ERROR = -1
        private const val SUCCESS = 0
    }
}