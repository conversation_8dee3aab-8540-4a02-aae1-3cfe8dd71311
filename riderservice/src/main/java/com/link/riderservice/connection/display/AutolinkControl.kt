package com.link.riderservice.connection.display

import android.content.Context
import android.content.res.Configuration
import android.media.projection.MediaProjection
import android.os.Build
import android.util.DisplayMetrics
import android.util.Log
import android.view.Display
import android.view.WindowManager
import androidx.annotation.RequiresApi
import com.link.riderservice.R
import com.link.riderservice.api.RiderService
import com.link.riderservice.api.dto.NaviMode
import com.link.riderservice.connection.display.config.AlConfig
import com.link.riderservice.connection.display.config.VideoConfig
import com.link.riderservice.connection.display.protocol.ALIntegration
import com.link.riderservice.connection.display.protocol.project.GalReceiver
import com.link.riderservice.connection.display.protocol.project.Protos
import com.link.riderservice.connection.display.transport.Transport
import com.link.riderservice.connection.display.video.MediaProjectService
import com.link.riderservice.utils.mainScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException

/**
 * <AUTHOR>
 * @date 2017/10/03
 * @desc Autolink Control for everything.
 */
internal class AutolinkControl(private val mContext: Context) {
    private var mGal: ALIntegration? = null
    private var mIsLandscape = 0
    private var mScreenWidth = 0
    private var mScreenHeight = 0
    private val mPhoneName: String = Build.MODEL
    private var mOnControlListener: OnControlListener? = null
    private var mLastRotation = 0
    private var mMediaProjectService: MediaProjectService
    private var mFile: File? = null
    private var mOutputStream: FileOutputStream? = null
    private val rotation: Int
        get() = (mContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager).defaultDisplay
            .rotation

    interface OnControlListener {
        /**
         * 连接丢失
         */
        fun onLoseConnected()

        /**
         * 不能恢复的错误
         * @param err 错误 code
         */
        fun onUnrecoverableError(err: Int)

        /**
         * 连接断开
         */
        fun onDisconnected()

        /**
         * 再见请求
         * @param reason 原因
         */
        fun onByeByeRequest(reason: Int)

        /**
         * 再见请求回复
         */
        fun onByeByeResponse()

        /**
         * 状态改变
         * @param status 状态
         * @see Protos
         */
        fun onStatusChanged(status: Int)

        /**
         * 初始化虚拟显示屏
         * @param display
         */
        fun onDisplayInitialized(display: Display?)

        /**
         * 释放虚拟显示屏
         */
        fun onDisplayReleased(display: Display)

        /**
         * 请求横屏，仪表盘不适用
         * @param isLandscape 横屏或者竖屏
         */
        @Deprecated("仪表盘不适用")
        fun requestLandscape(isLandscape: Boolean)

        /**
         * 请求开启自动旋转
         * @param isAutoed 是否自动旋转
         */
        @Deprecated("仪表盘不适用")
        fun onRequestAutoRotation(isAutoed: Boolean)

        /**
         * 停止传输
         */
        fun stopTransport()

        /**
         * 视频频道已经准备好，可以投屏
         */
        fun onVideoChannelReady()
        fun onRequestMediaProjection()
        fun onMirrorStart()
        fun onMirrorStop()
    }

    /**
     * 注册监听
     * @param listener 监听
     * @see OnControlListener
     */
    fun registerListener(listener: OnControlListener?) {
        mOnControlListener = listener
    }

    /**
     * 发送当前的 focus 给平台，该函数为了控制平台的焦点
     * @param focus [Protos.VIDEO_FOCUS_PROJECTED] or [Protos.VIDEO_FOCUS_NATIVE]
     * @param reason 原因
     * @see Protos
     */
    fun setEncoderState(focus: Int, reason: Int) = mGal?.sendEncoderState(focus, reason)


    /**
     * 发送手机屏幕信息给平台
     * @param isLandscape 是否横屏
     * @param rotation 旋转角度
     */
    fun sendOrientation(isLandscape: Int, rotation: Int) {
        mGal?.sendOrientation(isLandscape, rotation)
        mIsLandscape = isLandscape
        mLastRotation = rotation
    }

    /**
     * 发送再见请求给平台
     */
    fun sendByeByeRequest() = mGal?.sendByeByeRequest()

    fun setNaviMode(naviMode: NaviMode){
        mMediaProjectService.setNaviMode(naviMode)
    }

    fun setMediaProjection(mediaProjection: MediaProjection){
        mMediaProjectService.setMediaProjection(mediaProjection)
    }


    private fun updateAutolinkConfig() {
        val windowManager = mContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val outMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getRealMetrics(outMetrics)
        val orientation = mContext.resources.configuration.orientation
        mIsLandscape = orientation
        mLastRotation = rotation
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            mScreenWidth = outMetrics.heightPixels
            mScreenHeight = outMetrics.widthPixels
        } else {
            mScreenWidth = outMetrics.widthPixels
            mScreenHeight = outMetrics.heightPixels
        }
    }

    private fun sendResolutionNotification(width: Int, height: Int) {
        mGal?.sendResolutionNotification(width, height, false)
    }

    /**
     * 与车机建立连接
     * @param transport
     * @see Transport
     */
    fun startConnect(transport: Transport?) {
        startGal(transport)
    }

    /**
     * 释放资源
     */
    @Synchronized
    fun release() {
        Log.d(TAG,"autolink control release")
        mMediaProjectService.onShutdown()
        mGal?.destroy()
        mGal = null
        mOnControlListener = null
    }

    private fun createAlConfig(remoteAddress: String? = ""): AlConfig {
        return AlConfig(
            phoneName = mPhoneName,
            version = "1.0.11",
            appName = mContext.getString(R.string.config_name),
            screenWidth = mScreenWidth,
            screenHeight = mScreenHeight,
            isLandscape = mIsLandscape,
            rotation = mLastRotation,
            videoCfg = VideoConfig(isSupportH264 = true, isSupportMpeg4 = false),
            remoteHost = remoteAddress
        )
    }

    private fun startGal(transport: Transport?) {
        mGal = ALIntegration()
        mGal?.init(
            config = createAlConfig(),
            videoListener = mMediaProjectService,
            appMessageListener = object : GalReceiver.AppMessageListener {
                override fun onAutoRotationRequest(isAutoed: Boolean) {
                    mOnControlListener?.onRequestAutoRotation(isAutoed)
                }

                override fun onDisconnected() {
                    mOnControlListener?.onDisconnected()
                }

                override fun onUnrecoverableError(err: Int) {
                    mOnControlListener?.onUnrecoverableError(err)
                }

                override fun onVersionCallback(major: Short, minor: Short) {
                    RiderService.instance.setAutolinkVersion("$major.$minor")
                }

                override fun onCarInfoCallback(
                    id: String?,
                    make: String?,
                    model: String?,
                    year: String?,
                    huIc: String?,
                    huMake: String?,
                    huModel: String?,
                    huSwBuild: String?,
                    huSwVersion: String?,
                    huSeries: String?,
                    huMuVersion: String?,
                    checkSum: Int
                ) {

                }

                override fun onScreenOrientationInquire() {
                    sendResolutionNotification(mScreenWidth, mScreenHeight)
                }

                override fun onForceLandscapeRequest(isLandscape: Boolean) {
                    mOnControlListener?.requestLandscape(isLandscape)
                }

                override fun onExitRequest() {
                    mGal?.exit()
                }

                override fun onRunningState(state: Protos.State) {
                    mOnControlListener?.onStatusChanged(state.status)
                    if (state == Protos.State.PROBE_SUCCESS) {
                        mGal?.startAllChannel()
                    }
                }

                override fun onVideoChannelReady() {
                    mOnControlListener?.onVideoChannelReady()
                }

            },
            byeByeHandler = object : GalReceiver.ByeByeHandler {
                override fun onByeByeRequest(reason: Int) {
                    mGal?.sendByeByeRequest()
                    mOnControlListener?.onByeByeRequest(reason)
                }

                override fun onByeByeResponse() {
                    mOnControlListener?.onByeByeResponse()
                }

            }
        )
        mGal?.start(transport)
    }

    init {
        //initH264File()
        updateAutolinkConfig()
        mMediaProjectService = MediaProjectService(
            mContext,
            onDisplayInitialized = {
                mOnControlListener?.onDisplayInitialized(it)
            },
            onDisplayReleased = {
                mOnControlListener?.onDisplayReleased(it)
            },
            onRequestMediaProjection = {
                mOnControlListener?.onRequestMediaProjection()
            },
            onMirrorStart = {
                mOnControlListener?.onMirrorStart()
            },
            onMirrorStop = {
                mOnControlListener?.onMirrorStop()
            },
            sendFrame = {
                //dumpH264Raw(it.data)
                mGal?.sendFrame(it)
            }
        )
    }

    private fun initH264File() {
        mFile = File(mContext.getExternalFilesDir(null), "test.H264")
        try {
            mOutputStream = FileOutputStream(mFile)
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
        }
    }

    private fun dumpH264Raw(data: ByteArray) {
        mainScope.launch(Dispatchers.IO) {
            try {
                mOutputStream?.write(data)
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    fun requestLockScreenDisplay() {
        mGal?.requestLockScreenDisplay()
    }

    companion object {
        private const val TAG = "AutolinkControl"
    }
}