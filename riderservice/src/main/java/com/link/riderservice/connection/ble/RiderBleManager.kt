package com.link.riderservice.connection.ble

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.location.LocationManager
import android.os.ParcelUuid
import android.util.Log
import com.link.riderservice.api.RiderService
import com.link.riderservice.ble.data.Data
import com.link.riderservice.ble.scanner.BluetoothLeScannerCompat
import com.link.riderservice.ble.scanner.ScanCallback
import com.link.riderservice.ble.scanner.ScanFilter
import com.link.riderservice.ble.scanner.ScanResult
import com.link.riderservice.ble.scanner.ScanSettings
import com.link.riderservice.ext.collectWithScope
import com.link.riderservice.utils.BleUtils
import com.link.riderservice.utils.TimeUtils
import com.link.riderservice.utils.mainScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference
import java.util.UUID

/**
 * <AUTHOR>
 * @date 2022/8/25
 */
internal class RiderBleManager private constructor() {
    private val devices: MutableList<BleDevice> = ArrayList()
    private var mBleManager: BleManagerImpl? = null
    private var mBleDevice: BleDevice? = null
    private var isScanning = false
    private var isConfigConnect = false
    private var isAutoConnect = true
    private val mCallbacks: MutableList<WeakReference<RiderBleCallback>> = mutableListOf()
    private var lastScanTime: Long = 0
    private var delayScanJob: Job? = null

    @Synchronized
    fun addCallback(callback: RiderBleCallback) {
        mCallbacks.add(WeakReference(callback))
    }

    @Synchronized
    fun removeCallback(callback: RiderBleCallback) {
        mCallbacks.removeIf { it.get() == callback }
    }

    fun isConfigConnect(): Boolean {
        return isConfigConnect
    }

    fun getConnectAddress(): String {
        return mBleDevice!!.device.address
    }

    fun connect(device: BleDevice, context: Context) {
        stopScan()
        if (isConnected()) {
            return
        }
        mBleDevice = device
        mBleManager = BleManagerImpl(context, device.device, object : BleManagerCallback {
            override fun onDataReceived(device: BluetoothDevice, data: Data) {
                mCallbacks.forEach {
                    it.get()?.onDataReceived(device, data)
                }
            }

            override fun onDeviceConnecting(device: BluetoothDevice) {
                Log.d("connect analysis:", "start ble connect::${TimeUtils.getCurrentTimeStr()}")
                mCallbacks.forEach {
                    it.get()?.onDeviceConnecting(device)
                }
            }

            override fun onDeviceConnected(device: BluetoothDevice) {
                Log.d("connect analysis:", "end ble connect::${TimeUtils.getCurrentTimeStr()}")
                mCallbacks.forEach {
                    it.get()?.onDeviceConnected(device)
                }
            }

            override fun onDeviceFailedToConnect(device: BluetoothDevice, reason: Int) {
                mCallbacks.forEach {
                    it.get()?.onDeviceFailedToConnect(device, reason)
                }
            }

            override fun onDeviceReady(device: BluetoothDevice) {
                mCallbacks.forEach {
                    it.get()?.onDeviceReady(device)
                }
            }

            override fun onDeviceDisconnecting(device: BluetoothDevice) {
                mCallbacks.forEach {
                    it.get()?.onDeviceDisconnecting(device)
                }
            }

            override fun onDeviceDisconnected(device: BluetoothDevice, reason: Int) {
                Log.d(TAG, "onDeviceDisconnected")
                mCallbacks.forEach {
                    it.get()?.onDeviceDisconnected(device, reason)
                }
            }

            override fun onWriteRequestFailed(device: BluetoothDevice, status: Int) {

            }

            override fun onEnableNotificationFailed(device: BluetoothDevice, status: Int) {
                Log.d(TAG, "onEnableNotificationFailed: ")
                mCallbacks.forEach {
                    it.get()?.onEnableNotificationFailed(device, status)
                }
            }


        })
        mBleManager?.connect()
    }

    fun write(value: ByteArray) {
        mBleManager?.write(value)
    }

    fun release(isManual: Boolean = true) {
        Log.d(TAG, "release:$isManual")
        if (isManual) {
            mBleDevice = null
            stopScan()
        }
        //clearDevices()
        delayScanJob?.cancel()
        delayScanJob = null
        mBleManager?.release()
        mBleManager = null
    }

    private fun clearDevices() {
        devices.clear()
        mCallbacks.forEach {
            it.get()?.onScanResult(devices)
        }
    }

    fun closeConnect() {
        Log.d(TAG, "closeConnect")
        //clearDevices()
        mBleManager?.release()
        mBleManager = null
        isAutoConnect = false
    }

    fun isConnected(): Boolean {
        return mBleManager?.isConnected == true
    }


    private val bluetoothStateBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.STATE_OFF)
            val previousState = intent.getIntExtra(
                BluetoothAdapter.EXTRA_PREVIOUS_STATE, BluetoothAdapter.STATE_OFF
            )
            when (state) {
                BluetoothAdapter.STATE_ON -> {
                    Log.d(TAG, "Bluetooth is on")
                    startScan()
                }

                BluetoothAdapter.STATE_TURNING_OFF, BluetoothAdapter.STATE_OFF -> {
                    Log.d(TAG, "Bluetooth is off")
                    if (previousState != BluetoothAdapter.STATE_TURNING_OFF && previousState != BluetoothAdapter.STATE_OFF) {
                        stopScan()
                    }
                }
            }
        }
    }

    private val locationProviderChangedReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {

        }
    }

    private val scanCallback = object : ScanCallback() {
        override fun onScanResult(callbackType: Int, result: ScanResult) {
            super.onScanResult(callbackType, result)
            if (!isNoise(result)) {
                flow {
                    deviceDiscovered(result)
                    emit(devices.filter {
                        matchesUuidFilter(it.scanResult) && matchesNearbyFilter(it.rssi)
                    })
                }.collectWithScope(mainScope) { bleDevices ->
                    mCallbacks.forEach {
                        it.get()?.onScanResult(bleDevices)
                    }
                }
            }
        }

        override fun onBatchScanResults(results: MutableList<ScanResult>) {
            super.onBatchScanResults(results)

            if (results.isEmpty()) return
            flow {
                results.filter { !isNoise(it) }.forEach {
                    deviceDiscovered(it)
                }
                emit(devices.filter {
                    matchesUuidFilter(it.scanResult) && matchesNearbyFilter(it.rssi)
                })
            }.collectWithScope(mainScope) { bleDevices ->
                Log.d(TAG, "onBatchScanResults: ${bleDevices.size} devices found")
                mCallbacks.forEach {
                    it.get()?.onScanResult(bleDevices)
                }
                if (isAutoConnect) {
                    connectBestDevice(bleDevices)
                }
            }
        }

        override fun onScanFailed(errorCode: Int) {
            super.onScanFailed(errorCode)
            Log.w(TAG, "Scanning failed with code $errorCode")
        }
    }

    fun startScan() {
        if (mBleManager?.isConnected == true) {
            Log.d(TAG, "ble already connected")
            mCallbacks.forEach {
                it.get()?.onScanResult(devices)
            }
            return
        }

        if (!BleUtils.isBleEnabled()) {
            mCallbacks.forEach {
                it.get()?.onRequestBt()
            }
            return
        }

        if (!BleUtils.isLocationPermissionGranted(RiderService.instance.getApplication())) {
            mCallbacks.forEach {
                it.get()?.onNeedLocationPermission()
            }
            return
        }
        if (BleUtils.isSorAbove() && !BleUtils.isBluetoothScanPermissionGranted(RiderService.instance.getApplication())) {
            mCallbacks.forEach {
                it.get()?.onNeedBluetoothScanPermission()
            }
            return
        }

        val now = System.currentTimeMillis()
        val interval = now - lastScanTime
        if (isScanning) {
            Log.d(TAG, "ble already scanning")
            if (interval > SCANNING_DURATION) {
                stopScan()
                lastScanTime = now
                startScanInternal()
            } else {
                mCallbacks.forEach {
                    it.get()?.onScanning()
                }
            }
            return
        }
        if (now - lastScanTime < SLICE_SCANNING_PERIOD_MS) {
            Log.e(TAG, "startScan: too frequent, ${now - lastScanTime} ms")
            if (SCHEDULE_START_SCAN_WHEN_STARTED_TOO_FREQUENTLY) {
                val delay = SLICE_SCANNING_PERIOD_MS - now + lastScanTime
                Log.e(TAG, "startScan: schedule  start after $delay ms")
                delayScan(delay)
            }
        } else {
            lastScanTime = now
            startScanInternal()
        }
    }

    private fun startScanInternal() {
        Log.d("connect analysis:", "start ble scan::${TimeUtils.getCurrentTimeStr()}")
        val settings = ScanSettings.Builder()
            .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
            .setReportDelay(TIMEOUT_SCAN)
            .setUseHardwareBatchingIfSupported(false)
            .build()
        val filter = ScanFilter.Builder()
            .setManufacturerData(MANUFACTURE_ID, MANUFACTURE_DATA)
            .build()
        val filters = mutableListOf(filter)
        filters.add(filter)
        val scanner = BluetoothLeScannerCompat.getScanner()
        scanner.startScan(filters, settings, scanCallback)
        clearDevices()
        isScanning = true
        mCallbacks.forEach {
            it.get()?.onScanning()
        }
    }

    private fun delayScan(delay: Long) {
        if (delayScanJob?.isActive == true) {
            delayScanJob?.cancel()
        }
        delayScanJob = mainScope.launch(Dispatchers.IO) {
            delay(delay)
            startScan()
        }
    }

    fun stopScan() {
        Log.d("connect analysis:", "end ble scan::${TimeUtils.getCurrentTimeStr()}")
        if (isScanning) {
            val scanner = BluetoothLeScannerCompat.getScanner()
            scanner.stopScan(scanCallback)
        }
        isScanning = false
        mCallbacks.forEach {
            it.get()?.onScanFinish()
        }
    }

    @SuppressLint("MissingPermission")
    private fun connectBestDevice(devices: List<BleDevice>) {
        val sharedPref = RiderService.instance.getApplication()
            .getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
        var device: BleDevice? = null
        if (sharedPref.contains("ble_address")) {//检查是否存在ble键值对
            val address = sharedPref.getString("ble_address", "")
            Log.d(TAG, "already config connect: $address")
            device = devices.find { it.device.address == address }
            isConfigConnect = false
            device?.let {
                isConfigConnect = true
                connect(it, RiderService.instance.getApplication())
            }
        } else {
            device = findNearestDevice(devices)
            isConfigConnect = false
            device?.let {
                connect(it, RiderService.instance.getApplication())
            }
        }
    }

    private fun findNearestDevice(devices: List<BleDevice>): BleDevice? {
        return devices.sortedByDescending { it.rssi }.run {
            when {
                isEmpty() -> null
                size == 1 && first().rssi > -48 -> first()
                size > 1 -> {
                    val firstDevice = first()
                    val secondDevice = this[1]
                    val delta = firstDevice.rssi - secondDevice.rssi
                    Log.d(
                        TAG,
                        "findNearestDevice: ${firstDevice.rssi} - ${secondDevice.rssi} = $delta"
                    )
                    if (delta > 10 && firstDevice.rssi > -48) {
                        firstDevice
                    } else {
                        null
                    }
                }

                else -> null
            }
        }
    }

    private fun isNoise(result: ScanResult): Boolean {
        if (!result.isConnectable) return true
        if (result.rssi < FILTER_RSSI) return true
        if (BleUtils.isBeacon(result)) return true
        return BleUtils.isAirDrop(result)
    }

    @Synchronized
    private fun deviceDiscovered(result: ScanResult) {
        val index = devices.indexOfFirst { it.device.address == result.device.address }
        if (index == -1) {
            devices.add(BleDevice(result))
        } else {
            devices[index] = devices[index].update(result)
        }
    }

    private fun matchesUuidFilter(result: ScanResult): Boolean {
        if (!FILTER_UUID_REQUIRED) return true
        val record = result.scanRecord ?: return false
        val uuids = record.serviceUuids ?: return false
        return uuids.contains(FILTER_SERVICE_UUID)
    }

    private fun matchesNearbyFilter(rssi: Int): Boolean {
        return if (!FILTER_NEARBY_ONLY) true else (rssi in FILTER_RSSI..-1)
    }

    fun registerBroadcastReceivers(context: Context) {
        context.registerReceiver(
            bluetoothStateBroadcastReceiver, IntentFilter(BluetoothAdapter.ACTION_STATE_CHANGED)
        )
        context.registerReceiver(
            locationProviderChangedReceiver, IntentFilter(LocationManager.MODE_CHANGED_ACTION)
        )
    }

    fun unregisterBroadcastReceivers(context: Context) {
        try {
            context.unregisterReceiver(bluetoothStateBroadcastReceiver)
            context.unregisterReceiver(locationProviderChangedReceiver)
        } catch (ignore: Exception) {

        }
    }

    fun getCurrentConnectDevice(): BleDevice? {
        return mBleDevice
    }

    companion object {
        val instance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            RiderBleManager()
        }
        private const val FILTER_UUID_REQUIRED: Boolean = false
        private const val FILTER_NEARBY_ONLY: Boolean = true
        private const val FILTER_RSSI = -100 // [dBm]
        private val FILTER_SERVICE_UUID =
            ParcelUuid(UUID.fromString("0000ff00-0000-1000-8000-00805f9b34fb"))
        private const val MANUFACTURE_ID = 0x0AE7
        private val MANUFACTURE_DATA = byteArrayOf(0x72, 0x6C)
        private const val TIMEOUT_SCAN = 1_200L
        private const val NUM_SCAN_DURATIONS_KEPT = 5
        private const val EXCESSIVE_SCANNING_PERIOD_MS = 30 * 1000L
        private const val SLICE_SCANNING_PERIOD_MS = 6 * 1000L
        private const val SCANNING_DURATION = 10 * 1000L
        private const val SCHEDULE_START_SCAN_WHEN_STARTED_TOO_FREQUENTLY = true
        private const val TAG = "RiderBleManager"
    }
}