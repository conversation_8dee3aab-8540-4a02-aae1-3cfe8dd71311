package com.link.riderservice.inject

import com.link.riderservice.connection.ConnectionManager
import com.link.riderservice.message.MessageManager
import com.link.riderservice.weather.data.repository.WeatherRepositoryImpl

/**
 * Created on 2022/12/7.
 * <AUTHOR>
 */
internal object ModuleInject {

    val weatherRepository = WeatherRepositoryImpl()

    val connectionManager = ConnectionManager()

    val messageManager = MessageManager()
}