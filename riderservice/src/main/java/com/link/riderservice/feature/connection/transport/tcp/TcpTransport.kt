package com.link.riderservice.feature.connection.transport.tcp

import com.link.riderservice.core.utils.logging.logE
import com.link.riderservice.feature.connection.transport.Transport
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.net.Socket

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
class TcpTransport(
    private val socket: Socket, private val inputStream: InputStream = socket.getInputStream(),
    private val outputStream: OutputStream = socket.getOutputStream()
) : Transport {
    override fun stopTransport() {
        try {
            inputStream.close()
            outputStream.close()
            socket.close()
        } catch (e: IOException) {
            logE(TAG,"Failed to close streams",e)
        }
    }

    @Throws(IOException::class)
    override fun read(buffer: ByteArray, offset: Int, length: Int): Int {
        return inputStream.read(buffer, offset, length)
    }

    @Throws(IOException::class)
    override fun write(buffer: ByteArray, offset: Int, length: Int) {
        outputStream.write(buffer, offset, length)
    }

    override fun isConnected(): Boolean {
        return socket.isConnected
    }


    init {
        socket.tcpNoDelay = true
    }

    companion object {
        private const val TAG = "TcpTransport"
    }
}