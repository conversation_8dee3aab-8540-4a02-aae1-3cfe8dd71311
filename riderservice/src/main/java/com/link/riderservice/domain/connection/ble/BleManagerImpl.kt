package com.link.riderservice.domain.connection.ble

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothAdapter.STATE_DISCONNECTED
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothProfile.STATE_DISCONNECTING
import android.content.Context
import android.os.Build
import android.util.Log
import com.link.riderservice.BuildConfig
import com.link.riderservice.feature.ble.BleManager
import com.link.riderservice.feature.ble.PhyRequest
import com.link.riderservice.feature.ble.observer.ConnectionObserver
import com.link.riderservice.utils.logD
import com.link.riderservice.utils.logE
import java.util.UUID

/**
 * <AUTHOR>
 * @date 2022/8/9
 */
internal class BleManagerImpl(
    context: Context,
    val device: BluetoothDevice,
    private val callback: BleManagerCallback
) : BleManager(context) {
    private var targetCharacteristic: BluetoothGattCharacteristic? = null

    override fun isRequiredServiceSupported(gatt: BluetoothGatt): Boolean {
        targetCharacteristic = gatt.getService(SERVICE_UUID)?.getCharacteristic(CHARACTERISTICS)
        return true
    }

    override fun onServicesInvalidated() {
        targetCharacteristic = null
    }

    override fun initialize() {
        requestMtu(MTU).enqueue()
        setNotificationCallback(targetCharacteristic).with { device, data ->
            callback.onDataReceived(device, data)
        }
        beginAtomicRequestQueue()
            .add(enableNotifications(targetCharacteristic)
                .fail { device: BluetoothDevice, status: Int ->
                    logE(TAG, "Could not subscribe: $status")
                    callback.onEnableNotificationFailed(device, status)
                }
            )
            .done { logD(TAG, "Target initialized") }
            .enqueue()
    }

    override fun getMinLogPriority(): Int {
        return if (BuildConfig.DEBUG) Log.VERBOSE else Log.INFO
    }

    override fun log(priority: Int, message: String) {
        logD(TAG, message)
    }

    init {
        connectionObserver = object : ConnectionObserver {
            override fun onDeviceConnecting(device: BluetoothDevice) {
                callback.onDeviceConnecting(device)
            }

            override fun onDeviceConnected(device: BluetoothDevice) {
                callback.onDeviceConnected(device)
            }

            override fun onDeviceFailedToConnect(device: BluetoothDevice, reason: Int) {
                callback.onDeviceFailedToConnect(device, reason)
            }

            override fun onDeviceReady(device: BluetoothDevice) {
                callback.onDeviceReady(device)
            }

            override fun onDeviceDisconnecting(device: BluetoothDevice) {
                callback.onDeviceDisconnecting(device)
            }

            override fun onDeviceDisconnected(device: BluetoothDevice, reason: Int) {
                callback.onDeviceDisconnected(device, reason)
            }

        }
    }

    fun write(dataToWrite: ByteArray) {
        writeCharacteristic(
            targetCharacteristic,
            dataToWrite,
            BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT
        ).fail { device, status ->
            callback.onWriteRequestFailed(device, status)
        }.enqueue()
    }

    fun connect(timeout: Long = 20_000) {
        if (!isConnected) {
            val adapter = BluetoothAdapter.getDefaultAdapter()
            var preferredPhy = PhyRequest.PHY_LE_1M_MASK
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                if (adapter.isLe2MPhySupported) {
                    logD(TAG, "2M PHY supported")
                    preferredPhy = preferredPhy or PhyRequest.PHY_LE_2M_MASK
                }

                if (adapter.isLeCodedPhySupported) {
                    logD(TAG, "Coded PHY supported")
                    preferredPhy = preferredPhy or PhyRequest.PHY_LE_CODED_MASK
                }
            }

            connect(device)
                .usePreferredPhy(preferredPhy)
                .retry(5, 500)
                .fail { _, status ->
                    logE(TAG, "----connect request fail----status=$status")
                }
                .timeout(timeout)
                .enqueue()
        }
    }

    fun release() {
        if (connectionState == STATE_DISCONNECTED || connectionState == STATE_DISCONNECTING) {
            close()
            return
        }
        disconnect().enqueue()
    }

    companion object {
        private const val MTU = 256
        private val SERVICE_UUID: UUID = UUID.fromString("00001819-0000-1000-8000-00805f9b34fb")
        private val CHARACTERISTICS: UUID = UUID.fromString("00002a37-0000-1000-8000-00805f9b34fb")
        private const val TAG = "RiderBleManager"
    }
}