package com.link.riderservice.domain.connection.ble

import android.bluetooth.BluetoothDevice
import com.link.riderservice.feature.ble.data.Data

/**
 * <AUTHOR>
 * @date 2022/8/9
 */
interface BleManagerCallback {
    fun onDataReceived(device: BluetoothDevice, data: Data)
    fun onDeviceConnecting(device: BluetoothDevice)
    fun onDeviceConnected(device: BluetoothDevice)
    fun onDeviceFailedToConnect(device: BluetoothDevice, reason: Int)
    fun onDeviceReady(device: BluetoothDevice)
    fun onDeviceDisconnecting(device: BluetoothDevice)
    fun onDeviceDisconnected(device: BluetoothDevice, reason: Int)
    fun onWriteRequestFailed(device: BluetoothDevice, status: Int)
    fun onEnableNotificationFailed(device: BluetoothDevice, status: Int)
}